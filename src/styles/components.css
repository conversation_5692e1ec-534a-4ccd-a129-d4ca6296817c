/* 统一的组件样式定义 - 避免重复和冲突 */

/* === 表单组件 === */
.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs); /* 8px - 标签和控件之间的间距 */
  width: 100%;
  overflow: hidden;
}

.form-group label {
  color: var(--color-content-regular);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
}


.form-fieldset {
  border: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
}

/* === 按钮基础样式 === */
.button-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  height: var(--button-height);
  border: none;
  border-radius: var(--radius-base);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-sm) var(--spacing-md);
  cursor: pointer;
  transition: var(--transition-base);
  text-decoration: none;
  box-sizing: border-box;
}

.button-base:disabled {
  opacity: var(--opacity-muted);
  cursor: not-allowed;
}

.button-base__icon {
  width: var(--icon-size-medium);
  height: var(--icon-size-medium);
}

/* 按钮图标样式 */
.primary-button__icon,
.secondary-button__icon {
  width: var(--icon-size-medium);
  height: var(--icon-size-medium);
}

/* 按钮尺寸变体 */
.button-base--small {
  height: var(--button-height-small);
  padding: var(--spacing-xs) var(--spacing-base);
  font-size: var(--font-size-sm);
}

.button-base--large {
  height: var(--button-height-large);
  padding: var(--spacing-sm) var(--spacing-lg);
  font-size: var(--font-size-lg);
}

.button-base--full-width {
  width: 100%;
}

/* === 模态框组件 === */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--loading-backdrop);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: modal-fade-in var(--transition-base) ease-out;
}

.modal-container {
  background-color: var(--color-bg-dialog);
  border-radius: var(--radius-lg);
  width: 100%;
  max-width: var(--modal-width-default);
  box-shadow: var(--shadow-modal);
  border: var(--border-width) solid var(--color-border);
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: modal-slide-in var(--animation-duration) ease-out;
}

.modal-container--small {
  max-width: var(--modal-width-small);
}

.modal-container--large {
  max-width: var(--modal-width-large);
}

.modal-container--full {
  max-width: 90vw;
  max-height: 90vh;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  border-bottom: var(--border-width) solid var(--color-border);
  flex-shrink: 0;
  background-color: var(--color-bg-dialog);
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.modal-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  margin: 0;
  color: var(--color-content-accent);
}

.modal-content {
  padding: var(--spacing-lg);
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-base);
  margin-top: var(--spacing-lg);
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-base);
  padding:var(--spacing-md);
  border-top: var(--border-width) solid var(--color-border);
  flex-shrink: 0;
  background-color: var(--color-bg-dialog);
  border-radius: 0 0 var(--radius-lg) var(--radius-lg);
}

/* === 模态框表单 === */
.modal-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-base); /* 24px - 表单分组之间的主要间距 */
  width: 100%;
}

.modal-form .form-row {
  display: flex;
  gap: var(--spacing-base);
  align-items: flex-start;
}

.modal-form .form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs); /* 8px - 标签和控件之间的间距 */
  width: 100%;
  overflow: hidden;
  flex: 1;
}

/* === 预览组件 === */
.preview-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
}

.preview-sphere {
  width: 100%;
  aspect-ratio: 1;
  border-radius: var(--radius-base);
  background-color: var(--color-bg-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  border: var(--border-width) solid var(--color-border);
}

/* === 缩略图组件 === */
.thumbnail-container {
  width: var(--spacing-giant);
  height: var(--spacing-giant);
  border: var(--border-width) solid var(--color-border);
  border-radius: var(--radius-base);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: border-color 0.2s, background-color 0.2s;
  background-color: var(--color-bg-overlay);
  overflow: hidden;
}

.thumbnail-container:hover,
.thumbnail-container.drag-over {
  border-color: var(--color-brand);
  background-color: var(--color-bg-hover);
}

.thumbnail-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail-placeholder {
  width: var(--thumbnail-default);
  height: var(--thumbnail-default);
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-bg-overlay);
  border-radius: var(--radius-sm);
}

.thumbnail-placeholder-icon {
  color: var(--color-content-secondary);
}

/* === 表格组件 === */
.admin-table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--font-size-base);
  color: var(--color-content-regular);
  background-color: var(--color-bg-overlay);
  border-radius: var(--radius-base);
}

.admin-table th,
.admin-table td {
  padding: var(--spacing-base) var(--spacing-md);
  text-align: left;
  border-bottom: var(--border-width) solid var(--color-border);
  vertical-align: middle;
}

/* 移除最后一行的分割线 */
.admin-table tbody tr:last-child td {
  border-bottom: none;
}

.admin-table th {
  font-weight: var(--font-weight-medium);
  white-space: nowrap;
  color: var(--color-content-accent);
}

.admin-table tbody tr {
  height: var(--table-row-height);
  transition: background-color var(--transition-base);
}

.admin-table tbody tr:hover {
  background-color: var(--color-bg-hover);
}

.admin-table .thumbnail-cell {
  width: var(--table-cell-thumbnail);
  min-width: var(--thumbnail-default);
  max-width: var(--thumbnail-large);
}

.admin-table .date-cell {
  white-space: nowrap;
  min-width: var(--table-cell-min-width);
}

.admin-table .actions-cell {
  width: var(--table-cell-actions);
  text-align: left;
}

.admin-table .actions-cell > .icon-button + .icon-button {
  margin-left: var(--spacing-sm);
}

/* === 动画 === */
@keyframes modal-fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes modal-slide-in {
  from {
    opacity: 0;
    transform: translateY(calc(-1 * var(--spacing-lg))) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* === 加载容器 === */
.loading-container {
  position: relative;
  width: 100%;
  min-height: var(--preview-min-height);
}

/* === 颜色预览 === */
.color-preview {
  width: var(--color-preview-size);
  height: var(--color-preview-size);
  border-radius: var(--radius-sm);
  background-color: var(--preview-color);
  border: var(--border-width) solid var(--color-border);
  display: inline-block;
}

/* === 表格列样式 === */
.admin-table .model-name {
  min-width: var(--table-cell-name-min);
}

.admin-table .file-size {
  white-space: nowrap;
}

/* === 隐藏元素 === */
.hidden {
  display: none;
}

/* === 进度条 === */
.progress-bar {
  width: var(--progress-width, 0%);
  height: var(--spacing-xs);
  background: var(--color-primary);
  border-radius: var(--radius-xs);
  transition: width var(--transition-base);
}

/* Static material preview */
.static-material-preview {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: var(--border-width) solid var(--color-border-light);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  box-sizing: border-box;
}

/* Material preview canvas */
.material-preview-canvas {
  border-radius: 50%;
  display: block;
  width: 100%;
  height: 100%;
}

/* Tooltip positioning */
.tooltip-absolute {
  position: absolute;
}

/* Slider dynamic styles */
.slider-dynamic-width {
  /* Width will be set via CSS custom property */
  width: var(--slider-width, var(--slider-default-width));
}

.slider-fill-dynamic {
  /* Fill width will be set via CSS custom property */
  width: var(--fill-percentage, 0%);
}

/* Simple thumbnail container */
.simple-thumbnail-container {
  width: var(--thumbnail-size, var(--thumbnail-small));
  height: var(--thumbnail-size, var(--thumbnail-small));
  border-radius: 50%;
  overflow: hidden;
  background: var(--color-bg-overlay);
  position: relative;
}

/* Simple thumbnail canvas */
.simple-thumbnail-canvas {
  width: 100%;
  height: 100%;
  display: block;
}

/* Tab item dynamic width */
.tab-item-dynamic-width {
  width: var(--tab-width, var(--tab-width-default));
}

/* Input box dynamic width */
.input-box-dynamic-width {
  width: var(--input-width, auto);
}

/* === 透明背景 === */
.transparent-bg {
  background: transparent;
}

/* === 标签页内容 === */
.tab-content {
  display: block;
}

.tab-content--hidden {
  display: none;
}

/* === 相对定位容器 === */
.relative-container {
  position: relative;
}

/* === 圆形画布 === */
.canvas-circle {
  border-radius: 50%;
  width: 100%;
  height: var(--preview-canvas-height);
}

/* === 响应式设计 === */
@media (max-width: var(--breakpoint-small)) {
  .modal-container {
    max-width: 95vw;
    margin: var(--spacing-md);
  }
  
  .modal-actions {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
  
  .button-base--full-width {
    width: 100%;
  }
}