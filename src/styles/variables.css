/* 共享变量定义 */
:root {
    /* === 字体 === */
    --font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
    --font-size-sm: 12px;
    --font-size-base: 14px;
    --font-size-lg: 16px;
    --font-size-xl: 18px;
    --font-size-xxl: 20px;
    --font-size-xxxl: 32px;
    --font-weight-light: 300;
    --font-weight-regular: 400;
    --font-weight-medium: 500;
    --line-height-base: 1.5;

    /* === 颜色 === */
    --color-success: #008e5e;
    --color-error: #EF4444;
    --color-content-invert: #FFFFFF;

    /* === 材质默认颜色 === */
    --color-material-default: #B39B9C;

    /* === 阴影 === */
    --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
    --shadow-hover: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
    --shadow-modal: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);

    /* === 圆角 === */
    --radius-xs: 2px;
    --radius-sm: 4px;
    --radius-s: 6px;
    --radius-base: 8px;
    --radius-m: 12px;
    --radius-lg: 16px;
    --radius-full: 9999px;

    /* === 加载组件 === */
    --loading-backdrop: rgba(0, 0, 0, 0.7);
    --loading-container-bg: rgba(0, 0, 0, 0.7);
    --loading-container-backdrop-filter: blur(10px);

    /* === 模型卡片 === */
    --card-border-default: rgba(255, 255, 255, 0.15);
    --card-border-hover: rgba(255, 255, 255, 0.4);
    --card-shadow-inset: 0px 1px 3px 0px rgba(255, 255, 255, 0.15) inset;
    --card-shadow-hover: 0px 1px 3px 0px rgba(255, 255, 255, 0.35) inset, 0 16px 48px rgba(0, 0, 0, 0.2);

    /* === 间距 === */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-base: 12px;
    --spacing-md: 16px;
    --spacing-lg: 20px;
    --spacing-xl: 24px;
    --spacing-xxl: 32px;
    --spacing-xxxl: 40px;
    --spacing-huge: 48px;
    --spacing-massive: 80px;
    --spacing-giant: 100px;

    /* === 透明度 === */
    --opacity-disabled: 0.6;
    --opacity-muted: 0.5;
    --opacity-subtle: 0.7;
    --opacity-secondary: 0.8;

    /* === 欢迎页面特定 === */
    --welcome-bg-gradient: radial-gradient(circle at 50% 0%, #000000 46.04%, #0015ff 100%);
    --welcome-title-gradient: linear-gradient(90deg, #ffffff 0%, #a0b8ff 100%);

    /* === 组件尺寸 === */
    --button-height: 32px;
    --button-height-small: 28px;
    --button-height-large: 40px;
    --button-width-management: 120px;
    --input-height-small: 32px;
    --input-height-medium: 36px;
    --input-height-large: 42px;
    --icon-size-small: 14px;
    --icon-size-medium: 16px;
    --icon-size-large: 20px;
    --tooltip-width: 150px;
    
    /* === 图标尺寸 === */
    --size-icon-sm: 24px;
    --size-icon-md: 32px;
    
    /* === 上传组件尺寸 === */
    --size-upload-small: 100px;
    --size-upload-medium: 140px;
    --size-upload-large: 180px;
    --size-upload-image: 120px;
    --size-upload-image-small: 80px;
    --size-upload-image-large: 160px;
    --size-upload-texture: 120px;
    --size-upload-modal-min: 200px;

    /* === 标签页尺寸 === */
    --tab-width-compact: 80px;
    --tab-width-default: 104px;
    --tab-width-wide: 140px;

    /* === 输入框宽度 === */
    --input-width-narrow: 180px;
    --input-width-default: 240px;
    --input-width-wide: 320px;
    --input-width-search: 240px;

    /* === 模态框尺寸 === */
    --modal-width-small: 360px;
    --modal-width-default: 480px;
    --modal-width-large: 640px;

    /* === 表格尺寸 === */
    --table-row-height: 80px;
    --table-cell-thumbnail: 80px;
    --table-cell-actions: 120px;
    --table-cell-min-width: 100px;
    --table-cell-name-min: 150px;

    /* === 缩略图尺寸 === */
    --thumbnail-default: 60px;
    --thumbnail-small: 40px;
    --thumbnail-large: 100px;

    /* === 预览尺寸 === */
    --preview-min-height: 300px;
    --preview-canvas-height: 200px;
    --color-preview-size: 20px;

    /* === 滑块默认宽度 === */
    --slider-default-width: 220px;

    /* === 下拉框尺寸 === */
    --dropdown-max-height: 240px;

    /* === 侧边栏尺寸 === */
    --sidebar-width: 220px;

    /* === 网格和卡片尺寸 === */
    --grid-min-width-large: 250px;
    --grid-min-width-medium: 200px;
    --card-padding: 20px;
    --card-gap: 16px;
    --card-gap-small: 12px;
    --card-gap-large: 20px;
    --card-margin-bottom: 30px;
    --card-margin-small: 16px;

    /* === 图标容器尺寸 === */
    --icon-container-size: 48px;

    /* === 变换距离 === */
    --transform-hover: -2px;
    --transform-small: 14px;
    
    /* === 过渡效果 === */
    --transition-base: all 0.2s ease;
    --transition-fast: all 0.15s ease;
    --animation-duration: 0.3s;
    
    /* === 行高 === */
    --line-height-tight: 1.2;
    --line-height-relaxed: 1.5;
    
    /* === 边框 === */
    --border-width: 1px;
    
    /* === 层级 === */
    --z-index-overlay: 10;
    
    /* === 轮廓 === */
    --outline-width: 2px;
    --outline-offset: 2px;

    /* === 响应式断点 === */
    --breakpoint-small: 600px;
    --breakpoint-mobile: 768px;
    --breakpoint-tablet: 1024px;
}

/* 暗色主题（默认）*/
.theme-dark {
    /* === 品牌色 === */
    --color-brand: #2269EC;
    --color-brand-hover: #174291;
    --color-primary: #007bff;
    --color-primary-light: rgba(0, 123, 255, 0.1);
  
    /* === 辅助色 === */
    --color-support: rgba(255, 255, 255, 0.12);
    --color-support-hover: rgba(255, 255, 255, 0.06);
  
    /* === 边框 === */
    --color-border: rgba(255, 255, 255, 0.08);
    --color-border-light: #e9ecef;
    --color-divider: #000000;
  
    /* === 内容颜色 === */
    --color-content-accent: rgb(227, 227, 227);
    --color-content-regular: rgb(183, 183, 183);
    --color-content-secondary: rgb(141, 141, 141);
    --color-content-mute: rgb(114, 114, 114);
    --color-text: #212529;
    --color-text-subtle: #6c757d;
    --color-text-secondary: #6c757d;
    --color-white: #ffffff;
  
    /* === 背景 === */
    --color-bg-page: #1A1A1A;
    --color-bg-dialog: #2e2e2e;
    --color-bg-primary: #272727;
    --color-bg: #ffffff;
    --color-bg-subtle: #f8f9fa;
    --color-bg-overlay: rgba(255, 255, 255, 0.08);
    --color-bg-hover: rgba(255, 255, 255, 0.05);
    
    /* === 覆盖层 === */
    --color-overlay-dark: rgba(0, 0, 0, 0.6);
    --color-overlay-darker: rgba(0, 0, 0, 0.8);
    
    /* === 危险色 === */
    --color-danger: #dc3545;
    --color-danger-dark: #c82333;
    
    /* === 圆角映射 === */
    --radius-md: var(--radius-base);
}