export interface RawModelData {
  id: string;
  name: string;
  thumbnailPath: string | null;
  fileType: string | null;
  size: string | null; // Size is stored as a string in the DB
  createdAt: string;
  filePath: string; // Relative path from the backend
}

export interface ModelData extends Record<string, unknown> {
  id: number;
  name: string;
  thumbnailPath: string | null;
  fileType: string | null;
  size: number | null;
  createdAt: string;
  filePath: string; // 文件访问URL
}

// Raw material data from the API, with properties nested in `data`
export interface RawMaterialData {
  id: string;
  model_id: number;
  name: string;
  thumbnailPath: string | null;
  data: {
    color: string;
    metalness: number;
    roughness: number;
    glass: number;
    opacity?: number;
  };
  createdAt: string;
}

// Unified material data structure for the frontend
export type MaterialData = RawMaterialData;

// Payload for creating a new material
export interface CreateMaterialPayload {
  model_id: number;
  name: string;
  data: {
    color: string;
    metalness: number;
    roughness: number;
    glass: number;
  };
  thumbnailPath?: string;
}

// Payload for updating an existing material
export interface UpdateMaterialPayload {
  name?: string;
  data?: {
    color?: string;
    metalness?: number;
    roughness?: number;
    transmission?: number;
  };
  thumbnailPath?: string;
}

// Payload for creating a new model
export interface CreateModelPayload {
  name: string;
  filePath: string;
  thumbnailPath: string | null;
  size?: number;
}

// Payload for updating an existing model
export interface UpdateModelPayload {
  name?: string;
  filePath?: string;
  thumbnailPath?: string | null;
  size?: number;
}

// API base URL configuration
const BASE_URL = import.meta.env.DEV ? 'http://localhost:3001/api' : '/api';

// Unified error handling for API requests
const handleApiResponse = async <T>(response: Response): Promise<T> => {
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: `HTTP error! status: ${response.status}` }));
    throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
  }
  // For 204 No Content, response.json() will fail, so we return a placeholder.
  if (response.status === 204) {
    return {} as T;
  }
  return response.json();
};

// Transform model data from API to frontend format
const transformModelData = (model: RawModelData): ModelData => {
  const sizeInBytes = model.size ? parseInt(model.size, 10) : null;
  // The backend now serves files from a static directory, but only returns the relative path.
  // We need to construct the full URL here for the frontend to use.
  const backendUrl = import.meta.env.DEV ? 'http://localhost:3001' : ''; 

  return {
    id: parseInt(model.id, 10),
    name: model.name,
    // Construct full URLs for thumbnail and model files
    thumbnailPath: model.thumbnailPath ? `${backendUrl}${model.thumbnailPath}` : null,
    fileType: model.fileType,
    size: sizeInBytes && !isNaN(sizeInBytes) ? sizeInBytes : null,
    createdAt: model.createdAt,
    filePath: `${backendUrl}${model.filePath}`,
  };
};



class ApiService {
  // --- Uploader ---
  async uploadFile(
    file: File,
    onUploadProgress?: (progress: number) => void,
    pathname?: string
  ): Promise<{ url: string; pathname: string; filePath: string; size: number; }> {
    try {
      const formData = new FormData();
      formData.append('file', file);
      if (pathname) {
        formData.append('pathname', pathname);
      }

      // Use XMLHttpRequest to support upload progress
      if (onUploadProgress) {
        return new Promise((resolve, reject) => {
          const xhr = new XMLHttpRequest();
          xhr.open('POST', `${BASE_URL}/upload`, true);
          
          xhr.upload.onprogress = (event) => {
            if (event.lengthComputable) {
              const progress = Math.round((event.loaded / event.total) * 100);
              onUploadProgress(progress);
            }
          };
          
          xhr.onload = () => {
            if (xhr.status >= 200 && xhr.status < 300) {
              const result = JSON.parse(xhr.responseText);
              resolve(result);
            } else {
              reject(new Error(xhr.statusText));
            }
          };
          
          xhr.onerror = () => reject(new Error('Upload failed'));

          xhr.send(formData);
        });
      } else {
        // Fallback to fetch for simpler cases without progress tracking
        const response = await fetch(`${BASE_URL}/upload`, {
          method: 'POST',
          body: formData,
        });
        return await handleApiResponse<{ url: string; pathname: string; filePath: string; size: number; }>(response);
      }
    } catch (error) {
      console.error('Upload failed:', error);
      throw new Error('File upload failed. Please check your network connection or contact the administrator.');
    }
  }

  // --- Model Methods ---
  async getModels(): Promise<ModelData[]> {
    try {
      const response = await fetch(`${BASE_URL}/models`);
      const models = await handleApiResponse<RawModelData[]>(response);
      return models.map(transformModelData);
    } catch (error) {
      console.error('Failed to fetch models:', error);
      return [];
    }
  }

  async getModel(id: string): Promise<ModelData | null> {
    try {
      const response = await fetch(`${BASE_URL}/models/${id}`);
      const model = await handleApiResponse<RawModelData>(response);
      return transformModelData(model);
    } catch (error) {
      console.error(`Failed to get model ${id}:`, error);
      return null;
    }
  }

  async addModel(modelData: CreateModelPayload): Promise<ModelData | null> {
    try {
      const response = await fetch(`${BASE_URL}/models`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(modelData),
      });
      const newModel = await handleApiResponse<RawModelData>(response);
      return transformModelData(newModel);
    } catch (error) {
      console.error('Failed to add model:', error);
      return null;
    }
  }
  
  async updateModel(id: string, data: UpdateModelPayload): Promise<ModelData | null> {
    try {
      const response = await fetch(`${BASE_URL}/models/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });
      const updatedModel = await handleApiResponse<RawModelData>(response);
      return transformModelData(updatedModel);
    } catch (error) {
      console.error(`Failed to update model ${id}:`, error);
      return null;
    }
  }

  async deleteModel(id: string): Promise<boolean> {
    try {
      const response = await fetch(`${BASE_URL}/models/${id}`, { method: 'DELETE' });
      await handleApiResponse(response);
      return true;
    } catch (error) {
      console.error(`Failed to delete model ${id}:`, error);
      return false;
    }
  }

  // --- Material Methods ---
  async getMaterials(): Promise<MaterialData[]> {
    try {
      const response = await fetch(`${BASE_URL}/materials`);
      const materials = await handleApiResponse<MaterialData[]>(response);
      return materials;
    } catch (error) {
      console.error('Failed to fetch materials:', error);
      return [];
    }
  }

  async createMaterial(data: CreateMaterialPayload): Promise<MaterialData> {
    try {
      const response = await fetch(`${BASE_URL}/materials`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });
      return await handleApiResponse<MaterialData>(response);
    } catch (error) {
      console.error('Failed to create material:', error);
      throw error;
    }
  }

  async updateMaterial(id: string, data: UpdateMaterialPayload): Promise<MaterialData> {
    try {
      const response = await fetch(`${BASE_URL}/materials/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });
      return await handleApiResponse<MaterialData>(response);
    } catch (error) {
      console.error(`Failed to update material ${id}:`, error);
      throw error;
    }
  }

  async deleteMaterial(id: string): Promise<boolean> {
    try {
      const response = await fetch(`${BASE_URL}/materials/${id}`, { method: 'DELETE' });
      await handleApiResponse(response);
      return true;
    } catch (error) {
      console.error(`Failed to delete material ${id}:`, error);
      return false;
    }
  }
}

// Export a single instance of the service
export const apiService = new ApiService();