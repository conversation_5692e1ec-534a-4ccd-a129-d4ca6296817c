import { useState, useEffect, useCallback } from 'react';
import { SearchBox } from '../../components/search-box';
import { PrimaryButton } from '../../components/primary-button/primary-button';
import { IconButton } from '../../components/icon-button/icon-button';
import { CustomMaterialPanel } from '../../components/custom-material-panel/custom-material-panel';
import { Loading } from '../../components/loading/loading';
import { Modal } from '../../components/modal/modal';
import { InputBox } from '../../components/input-box/input-box';
import { Pagination } from '../../components/pagination/pagination';
import type { MaterialSettings } from '../../components/custom-material-panel/custom-material-panel';
import { Trash2, Edit, Plus } from 'lucide-react';
import MaterialPreview from '../../components/material-preview/MaterialPreview';
import './AdminTable.css';
import './MaterialModal.css';

import { apiService } from '../../services/api';
import type { MaterialData, ModelData, CreateMaterialPayload, UpdateMaterialPayload } from '../../services/api';
import MaterialThumbnailSimple from '../../components/material-thumbnail/MaterialThumbnailSimple';

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const MaterialManagement = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [selectedMaterial, setSelectedMaterial] = useState<MaterialData | null>(null);
  const [loading, setLoading] = useState(false);
  const [materials, setMaterials] = useState<MaterialData[]>([]);
  const [models, setModels] = useState<ModelData[]>([]);
  
  // 分页相关状态
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  useEffect(() => {
    const loadMaterials = async () => {
      setLoading(true);
      try {
        const data = await apiService.getMaterials();
        setMaterials(data);
      } catch (error) {
        console.error('加载材质失败:', error);
      } finally {
        setLoading(false);
      }
    };
    loadMaterials();

    const loadModels = async () => {
      try {
        const modelData = await apiService.getModels();
        setModels(modelData);
      } catch (error) {
        console.error('加载模型列表失败:', error);
      }
    };
    loadModels();
  }, []);

  const deleteMaterial = async (id: string) => {
    if (window.confirm('确定要删除这个材质吗？')) {
      try {
        await apiService.deleteMaterial(id);
        setMaterials(materials.filter(material => material.id !== id));
      } catch (error) {
        console.error('删除材质失败:', error);
      }
    }
  };

  const handleSaveMaterial = async (materialData: CreateMaterialPayload | UpdateMaterialPayload) => {
    try {
      let savedMaterial: MaterialData;
      if (selectedMaterial) {
        savedMaterial = await apiService.updateMaterial(selectedMaterial.id, materialData);
        setMaterials(materials.map(m => m.id === savedMaterial.id ? savedMaterial : m));
      } else {
        savedMaterial = await apiService.createMaterial(materialData as CreateMaterialPayload);
        setMaterials([...materials, savedMaterial]);
      }
      setShowAddModal(false);
      setSelectedMaterial(null);
    } catch (error) {
      console.error('保存材质失败:', error);
      alert(`保存材质失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  };

  const handleOpenAddModal = () => {
    setSelectedMaterial(null);
    setShowAddModal(true);
  };

  const handleCloseModal = () => {
    setShowAddModal(false);
    setSelectedMaterial(null);
  };

  const filteredMaterials = materials.filter(material => 
    material.name.toLowerCase().includes(searchQuery.toLowerCase())
  );
  
  // 分页计算
  const totalItems = filteredMaterials.length;
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedMaterials = filteredMaterials.slice(startIndex, endIndex);
  
  // 当搜索结果变化时重置到第一页
  useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery]);
  
  // 处理分页变化
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };
  
  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1); // 重置到第一页
  };

  if (loading) {
    return (
      <div className="loading-container">
        <Loading 
          text="正在加载材质列表..." 
          centered={true}
        />
      </div>
    );
  }

  return (
    <div className="material-management">
      <div className="management-toolbar">
        <SearchBox
          placeholder="搜索材质"
          value={searchQuery}
          onChange={setSearchQuery}
          className="search-box--management"
        />
        <div className="toolbar-actions">
          <PrimaryButton icon={Plus} onClick={handleOpenAddModal}>
            添加材质
          </PrimaryButton>
        </div>
      </div>
      
      <div className="admin-table-container">
        <table className="admin-table">
          <thead>
            <tr>
              <th>缩略图</th>
              <th>材质名称</th>
              <th>颜色</th>
              <th>金属感</th>
              <th>粗糙度</th>
              <th>不透明度</th>
              <th>创建时间</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            {paginatedMaterials.map(material => (
              <tr key={material.id} className="material-row">
                <td className="thumbnail-cell">
                  <MaterialThumbnailSimple material={material} size={44} />
                </td>
                <td className="material-name">{material.name}</td>
                <td className="material-color">
                  <div
                    className="color-preview"
                    style={{ '--preview-color': material.data.color } as React.CSSProperties}
                    title={material.data.color}
                  ></div>
                </td>
                <td>{Math.round((material.data.metalness || 0) * 100)}%</td>
                <td>{Math.round((material.data.roughness || 0) * 100)}%</td>
                                <td>{Math.round((1 - (material.data.glass || 0)) * 100)}%</td>
                <td className="date-cell">{formatDate(material.createdAt)}</td>
                <td className="actions-cell">
                  <IconButton icon={Edit} size="small" onClick={() => setSelectedMaterial(material)} />
                  <IconButton icon={Trash2} size="small" onClick={() => deleteMaterial(material.id)} />
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      
      {/* 分页器 */}
      {totalItems > 0 && (
        <Pagination
          current={currentPage}
          total={totalItems}
          pageSize={pageSize}
          onChange={handlePageChange}
          onPageSizeChange={handlePageSizeChange}
          showSizeChanger={true}
          pageSizeOptions={[10, 20, 50]}
          showTotal={true}
        />
      )}
      
      {(showAddModal || selectedMaterial) && (
        <MaterialModal 
          key={selectedMaterial?.id || 'new'}
          material={selectedMaterial || undefined}
          models={models} // Pass models to the modal
          onClose={handleCloseModal} 
          onSave={handleSaveMaterial}
        />
      )}
    </div>
  );
};

interface MaterialModalProps {
  material?: MaterialData;
  models: ModelData[]; // Pass the list of models
  onClose: () => void;
  onSave: (data: CreateMaterialPayload | UpdateMaterialPayload) => void | Promise<void>;
}

const MaterialModal = ({ material, models, onClose, onSave }: MaterialModalProps) => {
  const [name, setName] = useState(material?.name || ''); // 移除标签页状态
  
  const [materialSettings, setMaterialSettings] = useState<MaterialSettings>(() => {
    if (material?.data) {
      return {
        ...material.data,
        opacity: 1 - (material.data.glass || 0),
      };
    }
    return {
      color: 'var(--color-material-default)',
      metalness: 0.5,
      roughness: 0.5,
      opacity: 1,
      glass: 0
    };
  });

  const handleSettingsChange = useCallback((settings: MaterialSettings) => {
    setMaterialSettings(prev => ({ ...prev, ...settings }));
  }, []);

  const handleSave = () => {
    const payload = {
      name,
      data: {
        color: materialSettings.color,
        metalness: materialSettings.metalness,
        roughness: materialSettings.roughness,
        glass: 1 - materialSettings.opacity,
      }
    };

    if (material) {
      onSave(payload as UpdateMaterialPayload);
    } else {
      if (models.length === 0) {
        alert('创建材质失败：没有可用的模型进行关联。');
        return;
      }
      const createPayload: CreateMaterialPayload = {
        ...payload,
        model_id: models[0].id, // 直接使用id，无需parseInt转换
      };
      onSave(createPayload);
    }
  };
  
  return (
    <Modal
      visible={true}
      title={material ? '编辑材质' : '添加新材质'}
      onClose={onClose}
      size="medium"
      footer={
         <PrimaryButton onClick={handleSave} showIcon={false}>
           保存
         </PrimaryButton>
       }
    >
      <div className="modal-form material-modal-form">
        {/* 材质名称区域 - 最上方单独一行 */}
        <div className="material-name-section">
          <div className="form-group">
            <label>材质名称</label>
            <InputBox
              type="text"
              value={name}
              onChange={setName}
              onEnter={handleSave}
              placeholder="输入材质名称"
              fullWidth
            />
          </div>
        </div>

        <div className="material-modal-layout">
          {/* 左侧预览区 */}
          <div className="material-modal-preview">
            <div className="preview-header">
              材质预览
            </div>
            <div className="preview-sphere">
              <MaterialPreview settings={materialSettings} size={0} />
            </div>
          </div>
          
          {/* 右侧设置区 */}
          <div className="material-modal-settings">
            {/* 直接显示材质设置，移除标签页 */}
            <div className="material-settings compact-settings">
              <CustomMaterialPanel
                defaultSettings={materialSettings}
                onChange={handleSettingsChange}
              />
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default MaterialManagement;