import { useState } from 'react';
import { InputBox } from '../../components/input-box/input-box';
import { PrimaryButton } from '../../components/primary-button/primary-button';
import './Login.css';
import { User, Lock } from 'lucide-react';
import LogoPng from '../../assets/images/Logo.png';

const Login = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');

  const handleLogin = () => {
    // 简单的验证逻辑，实际应用中应该与后端验证
    if (username === 'huitong' && password === 'admin123') {
      localStorage.setItem('isLoggedIn', 'true');
      window.location.href = '/admin/dashboard';
    } else {
      setError('用户名或密码错误');
    }
  };

  return (
    <div className="login-container theme-dark">
      <div className="login-card">
        <div className="login-header">
          <img className="login-logo" src={LogoPng} alt="RINKO" />
          <h2>会通智能色彩云库 - 后台管理</h2>
        </div>
        
        <form className="login-form" onSubmit={(e) => { e.preventDefault(); handleLogin(); }}>
          <div className="form-group">
            <label>用户名</label>
            <InputBox 
              placeholder="请输入用户名" 
              value={username} 
              onChange={setUsername}
              onEnter={handleLogin}
              prefixIcon={<User size={18} />}
              fullWidth
            />
          </div>
          
          <div className="form-group">
            <label>密码</label>
            <InputBox 
              placeholder="请输入密码" 
              value={password} 
              onChange={setPassword}
              onEnter={handleLogin}
              prefixIcon={<Lock size={18} />}
              type="password"
              fullWidth
            />
          </div>
          
          {error && <div className="login-error">{error}</div>}
          
          <PrimaryButton type="submit" fullWidth showIcon={false}>登录</PrimaryButton>
        </form>
      </div>
    </div>
  );
};

export default Login;
