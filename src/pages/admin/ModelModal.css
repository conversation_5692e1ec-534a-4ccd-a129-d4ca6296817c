/* 添加模型弹窗专用样式 */
/* 为表单分组之间添加合适的间距，提升用户体验 */

.model-modal-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-base); /* 24px - 表单分组之间的主要间距 */
  width: 100%;
}

/* 表单分组样式优化 */
.model-modal-form .form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs); /* 8px - 标签和控件之间的间距 */
  width: 100%;
  overflow: hidden;
}

/* 表单分组标签样式 */
.model-modal-form .form-group label {
  color: var(--color-content-regular);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
}

/* 上传状态区域样式 */
.model-modal-upload-status {
  margin-top: var(--spacing-xl) !important; /* 24px - 与上方表单分组的间距 */
  padding: var(--spacing-md); /* 16px - 内边距 */
  padding-top: var(--spacing-lg); /* 20px - 顶部内边距 */
  border: var(--border-width) solid var(--color-border);
  border-top: 1px solid var(--color-border); /* 顶部分隔线 */
  border-radius: var(--radius-base);
  background-color: var(--color-bg-overlay);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-base); /* 12px - 上传状态内部元素间距 */
}

/* 上传消息样式 */
.model-modal-upload-status .upload-message {
  font-size: var(--font-size-sm);
  color: var(--color-content-secondary);
  margin: 0;
  text-align: center;
}

/* 进度条容器样式 */
.model-modal-upload-status .progress-bar-container {
  width: 100%;
  height: var(--spacing-sm); /* 8px */
  background-color: var(--color-bg-hover);
  border-radius: var(--radius-xs);
  overflow: hidden;
}

/* 进度条样式 */
.model-modal-upload-status .progress-bar {
  height: 100%;
  background-color: var(--color-brand);
  border-radius: var(--radius-xs);
  transition: width var(--animation-duration) ease-in-out;
}

/* 上传百分比样式 */
.model-modal-upload-status .upload-percentage {
  font-size: var(--font-size-sm);
  color: var(--color-content-mute);
  text-align: center;
  font-weight: var(--font-weight-medium);
}

/* 禁用状态样式 */
.model-modal-form.form--disabled {
  opacity: 0.6;
  pointer-events: none;
}

/* 响应式设计 - 小屏幕优化 */
@media (max-width: 768px) {
  .model-modal-form {
    gap: var(--spacing-lg); /* 20px - 小屏幕上稍微减少间距 */
  }
  
  .model-modal-upload-status {
    margin-top: var(--spacing-md); /* 16px - 小屏幕上减少上边距 */
    padding: var(--spacing-sm); /* 12px - 小屏幕上减少内边距 */
  }
}

/* 表单分组间距已移除 */