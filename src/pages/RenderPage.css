/* 页面布局 */
.render-page {
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: var(--spacing-lg);
  box-sizing: border-box;
  background: var(--color-bg-page);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  color: var(--color-content-regular);
  position: fixed;
  inset: 0;
}

/* 标题栏 */
.title-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-left: var(--spacing-sm);
  width: 100%;
  margin-bottom: var(--spacing-lg);
}

.title-bar__left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.logo {
  width: 78px;
  height: 14px;
  transition: all 0.2s ease;
  object-fit: contain;
}

.logo--clickable {
  cursor: pointer;
  height: var(--button-height);
}

.user-controls {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: var(--spacing-sm);
}

/* 主要内容区域 */
.render-container {
  display: flex;
  flex: 1;
  gap: var(--spacing-lg);
  overflow: hidden;
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  width: 100%;
}

/* 渲染窗口 */
.render-window {
  flex: 1;
  min-width: 0;
  background: var(--color-bg-primary);
  border-radius: var(--radius-lg);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 渲染区域 */
.render-area {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-bg-primary);
  overflow: hidden;
  position: relative;
}

.render-area canvas {
  width: 100% !important;
  height: 100% !important;
  outline: none;
}

/* 控制按钮容器 */
.button-container {
  position: absolute;
  right: var(--spacing-lg);
  bottom: var(--spacing-lg);
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: var(--spacing-sm);
}

.control-button {
  padding: var(--spacing-xs) var(--spacing-sm);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  background: var(--color-bg-overlay);
  border-radius: var(--radius-full);
  cursor: pointer;
}

.control-button--clickable {
  transition: background-color 0.2s ease, color 0.2s ease;
}

.control-button--clickable:hover {
  background-color: var(--color-bg-hover);
  color: var(--color-content-accent);
}

.control-button span {
  color: var(--color-content-regular);
  font-size: var(--font-size-base);
}

.icon-wrapper {
  width: var(--spacing-md);
  height: var(--spacing-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-content-regular);
}

/* 属性面板 */
.property-panel {
  width: 220px;
  flex-shrink: 0;
  padding: var(--spacing-base);
  background: var(--color-bg-primary);
  border-radius: var(--radius-lg);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  overflow-y: auto;
  overflow-x: hidden;
}

.panel-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  width: 100%;
}

.panel-section--flex {
  flex: 1;
}

.section-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.section-header span {
  color: var(--color-content-regular);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
}

/* 模型选择区域 */
.dropdown-wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.upload-button-wrapper {
  width: 100%;
}

.uploaded-model-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--color-bg-overlay);
  border-radius: var(--radius-base);
  padding: var(--spacing-sm);
  height: var(--input-height-medium);
  width: 100%;
}

.uploaded-model-name {
  color: var(--color-content-regular);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  margin-right: var(--spacing-sm);
}

/* 材质设置区域 */
.editable-materials-grid {
  margin-top: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
}


.tab-item {
  font-size: var(--font-size-base);
  color: var(--color-content-regular);
}

.materials-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-base);
  min-height: 0;
  overflow: visible;
}

.search-wrapper {
  width: 100%;
  margin-bottom: var(--spacing-base);
}

/* 工具提示帮助内容 */
.tooltip-help-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.help-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  white-space: nowrap;
}