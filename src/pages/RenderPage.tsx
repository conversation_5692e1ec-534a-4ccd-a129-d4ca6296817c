import { useState, useRef, useEffect, Suspense, useCallback, useMemo } from 'react';
import type { RawMaterialData } from '../services/api';
import { useParams, useLocation, useNavigate } from 'react-router-dom';
import { useNotification } from '../components/notification/notification';
import { validateFile, FILE_VALIDATION_CONFIGS } from '../utils/fileUpload';
import { StatusMessage } from '../components/status-message/status-message';
import { EmptyState } from '../components/empty-state/empty-state';
import { useLogoClick } from '../hooks/useLogoClick';
import { clearTextureCache, normalizeModel, createStandardMaterial, disposeMaterial } from '../utils/threeUtils';
import { registerWebGLContext, safeReleaseContext } from '../utils/webglContextManager';
import './RenderPage.css';
import { SearchBox } from '../components/search-box';
import { PrimaryButton } from '../components/primary-button/primary-button';
import { SecondaryButton } from '../components/secondary-button/secondary-button';
import { IconButton } from '../components/icon-button/icon-button';
import { DropDown } from '../components/drop-down/drop-down';
import { TabGroup } from '../components/tab-group/tab-group';
import { TabItem } from '../components/tab-item/tab-item';
import { CustomMaterialPanel } from '../components/custom-material-panel/custom-material-panel';
import { ErrorBoundary } from '../components/error-boundary/error-boundary';
import { Loading } from '../components/loading/loading';
import { Tooltip } from '../components/tooltip/tooltip';
import { Copy, Download, Upload, HelpCircle, RotateCcw, Box, Paintbrush, Trash2, RotateCw, Move, ZoomIn } from 'lucide-react';
import LogoImage from '../assets/images/Logo.png';
import { Canvas } from '@react-three/fiber';
import { OrbitControls, Environment, useGLTF, Stage, Html } from '@react-three/drei';
import type { OrbitControls as OrbitControlsImpl } from 'three-stdlib';
import * as THREE from 'three';


import MaterialThumbnail from '../components/material-thumbnail/material-thumbnail';
import { MaterialsGrid } from '../components/materials-grid/materials-grid';

import { UploadModal } from '../components/upload';

import { apiService } from '../services/api';
import type { ModelData, MaterialData } from '../services/api';

interface MaterialProps {
  textureUrl?: string;
  color: string;
  metalness: number; // 0-1
  roughness: number; // 0-1
  opacity: number;   // 0-1, 1 为不透明
}

// 每个 Mesh 名称 -> 对应材质
type PerMeshMaterials = Record<string, MaterialProps>;

// 原始材质信息
interface OriginalMaterial {
  name: string;
  meshName: string;
  color: string;
  metalness: number;
  roughness: number;
  opacity: number;
  isEditable: boolean; // 是否以Editable开头
}

interface ModelProps {
  modelPath: string;
  perMeshMaterials: PerMeshMaterials;
  activeMaterialName: string | null; // 当前激活的材质名称，用于高亮
  onOriginalMaterialsExtracted: (materials: OriginalMaterial[]) => void;
}

const Model = ({ modelPath, perMeshMaterials, activeMaterialName, onOriginalMaterialsExtracted }: ModelProps) => {
  const { scene } = useGLTF(modelPath);
  const [materialsExtracted, setMaterialsExtracted] = useState(false);
  const materialsExtractedRef = useRef(false);
  const createdMaterialsRef = useRef<THREE.Material[]>([]);
  const originalMaterialsRef = useRef<Map<string, THREE.Material>>(new Map());
  const currentModelPathRef = useRef<string>(modelPath);
  


  // 性能监控和内存管理
  useEffect(() => {
    let memoryCheckInterval: NodeJS.Timeout;
    
    const checkMemoryUsage = () => {
      if ('memory' in performance) {
        const memory = (performance as { memory: { usedJSHeapSize: number; totalJSHeapSize: number } }).memory;
        const usedMB = memory.usedJSHeapSize / 1024 / 1024;
        const totalMB = memory.totalJSHeapSize / 1024 / 1024;
        
        // 如果内存使用超过80%，触发清理
        if (usedMB / totalMB > 0.8) {
          console.warn('High memory usage detected, triggering cleanup');
          clearTextureCache();
          if (window.gc) {
            window.gc();
          }
        }
      }
    };
    
    // 每30秒检查一次内存使用情况
    // eslint-disable-next-line prefer-const
    memoryCheckInterval = setInterval(checkMemoryUsage, 30000);
    
    return () => {
      if (memoryCheckInterval) {
        clearInterval(memoryCheckInterval);
      }
    };
  }, []);

  // 当模型路径变化时重置材质提取状态
  useEffect(() => {
    // 检查模型路径是否真的发生了变化
    if (currentModelPathRef.current !== modelPath) {
      console.log('Model path changed from', currentModelPathRef.current, 'to', modelPath);
      
      // 清理之前的模型路径缓存
      if (currentModelPathRef.current) {
        useGLTF.clear(currentModelPathRef.current);
      }
      
      currentModelPathRef.current = modelPath;
      
      setMaterialsExtracted(false);
      materialsExtractedRef.current = false;

      // 清理之前创建的材质
      createdMaterialsRef.current.forEach(material => {
        disposeMaterial(material);
      });
      createdMaterialsRef.current = [];

      // 清理原始材质缓存
      originalMaterialsRef.current.clear();
      
      // 强制垃圾回收
      if (window.gc) {
        window.gc();
      }
    }
  }, [modelPath]);

  // 组件卸载时清理材质和纹理
  useEffect(() => {
    return () => {
      createdMaterialsRef.current.forEach(material => {
        disposeMaterial(material);
      });
      // 清理纹理缓存（仅在组件完全卸载时）
      clearTextureCache();
    };
  }, []);
  


  // 材质提取逻辑 - 分离到独立的useEffect中
  useEffect(() => {
    if (!scene || materialsExtracted || materialsExtractedRef.current) return;

    try {
      // 统一模型归一化：重置、缩放、居中
      normalizeModel(scene, 2);

      materialsExtractedRef.current = true;
      const originalMaterials: OriginalMaterial[] = [];

      scene.traverse((object) => {
        if (object instanceof THREE.Mesh && object.material) {
          const material = object.material as THREE.MeshStandardMaterial;
          const materialName = material.name || `Material_${object.name}`;

          // 保存原始材质的克隆版本，避免修改原始材质
          originalMaterialsRef.current.set(object.name, material.clone());

          originalMaterials.push({
            name: materialName,
            meshName: object.name,
            color: `#${material.color.getHexString()}`,
            metalness: material.metalness || 0,
            roughness: material.roughness || 0.5,
            opacity: material.opacity || 1,
            isEditable: materialName.startsWith('Editable')
          });
        }
      });

      // 通知父组件原始材质信息
      onOriginalMaterialsExtracted(originalMaterials);
      setMaterialsExtracted(true);
    } catch (error) {
      console.error('Error extracting materials:', error);
      materialsExtractedRef.current = false;
    }
  }, [scene, onOriginalMaterialsExtracted, materialsExtracted]);

  // 材质应用逻辑 - 分离到独立的useEffect中
  useEffect(() => {
    if (!scene || !materialsExtracted) return;

    scene.traverse((object) => {
      if (object instanceof THREE.Mesh && object.material) {
        try {
          // 若用户为该 mesh 指定了材质，则覆盖原始材质
          if (perMeshMaterials[object.name]) {
            const mat = perMeshMaterials[object.name];

            // 如果已经有自定义材质，先清理它
            if (object.material instanceof THREE.MeshStandardMaterial &&
                createdMaterialsRef.current.includes(object.material)) {
              disposeMaterial(object.material);
              const index = createdMaterialsRef.current.indexOf(object.material);
              if (index > -1) {
                createdMaterialsRef.current.splice(index, 1);
              }
            }

            const newMat = createStandardMaterial({
              color: mat.color,
              metalness: mat.metalness,
              roughness: mat.roughness,
              opacity: mat.opacity,
              textureUrl: mat.textureUrl
            });

            if (newMat) {
              // 保持材质名称以便高亮功能正常工作
              const originalMaterial = originalMaterialsRef.current.get(object.name);
              if (originalMaterial) {
                newMat.name = originalMaterial.name;
                
                // 确保新材质的发光属性与原始材质一致
                if (originalMaterial instanceof THREE.MeshStandardMaterial && 
                    originalMaterial.userData.originalEmissive) {
                  newMat.userData.originalEmissive = originalMaterial.userData.originalEmissive.clone();
                } else {
                  newMat.userData.originalEmissive = new THREE.Color(0x000000);
                }
              }

              object.material = newMat;
              // 跟踪新创建的材质
              createdMaterialsRef.current.push(newMat);
            }
          } else {
            // 如果没有自定义材质，但之前有过，则恢复原始材质
            const originalMaterial = originalMaterialsRef.current.get(object.name);
            if (originalMaterial && object.material !== originalMaterial) {
              // 清理当前的自定义材质
              if (object.material instanceof THREE.MeshStandardMaterial &&
                  createdMaterialsRef.current.includes(object.material)) {
                disposeMaterial(object.material);
                const index = createdMaterialsRef.current.indexOf(object.material);
                if (index > -1) {
                  createdMaterialsRef.current.splice(index, 1);
                }
              }
              object.material = originalMaterial;
            }
          }
        } catch (error) {
          console.error('Error applying material to mesh:', object.name, error);
        }
      }
    });
  }, [scene, perMeshMaterials, materialsExtracted]);

  // 高亮效果逻辑 - 分离到独立的useEffect中
  useEffect(() => {
    if (!scene || !materialsExtracted) return;

    // 创建一个临时的发光材质，用于高亮显示
    const highlightEmissive = new THREE.Color(0x333333);
    
    scene.traverse((object) => {
      if (object instanceof THREE.Mesh && object.material) {
        const material = object.material as THREE.MeshStandardMaterial;
        const materialName = material.name || `Material_${object.name}`;

        // 确保所有材质都有相同的基础属性设置
        if (!material.userData.originalEmissive) {
          // 首次处理该材质时，保存原始发光值
          material.userData.originalEmissive = material.emissive.clone();
        }

        if (activeMaterialName && materialName === activeMaterialName) {
          // 添加发光效果表示激活状态，使用固定值避免差异
          if (material.emissive) {
            material.emissive.copy(highlightEmissive);
          }
        } else {
          // 恢复原始发光效果
          if (material.emissive && material.userData.originalEmissive) {
            material.emissive.copy(material.userData.originalEmissive);
          } else if (material.emissive) {
            material.emissive.setHex(0x000000);
          }
        }
      }
    });
  }, [scene, activeMaterialName, materialsExtracted]);

  return (
    <group>
      <primitive object={scene} />
    </group>
  )
}

const Loader = () => {
  return (
    <Html center>
      <Loading />
    </Html>
  );
};

const RenderPage = () => {
  const location = useLocation();
  const controlsRef = useRef<OrbitControlsImpl | null>(null);
  const { modelId } = useParams<{ modelId?: string }>();
  const notify = useNotification();
  const navigate = useNavigate();
  const { handleLogoClick } = useLogoClick('/admin');

  // 重置相机到默认视角
  const resetView = useCallback(() => {
    if (controlsRef.current) {
      controlsRef.current.reset();
      controlsRef.current.target.set(0, 0, 0);
      controlsRef.current.object.position.set(0, 1, 4);
      controlsRef.current.update();
    }
  }, []);
  // 初始为空，等后台数据返回后自动赋值
  const [selectedModelId, setSelectedModelId] = useState<string>(modelId || '');
  
  // 当URL参数中的modelId变化时，更新selectedModelId
  useEffect(() => {
    if (modelId && modelId !== selectedModelId) {
      setSelectedModelId(modelId);
    }
  }, [modelId, selectedModelId]);
  const [searchQuery, setSearchQuery] = useState('');
  // 当前选中的系统预设材质 ID
  const [selectedPresetId, setSelectedPresetId] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('会通材料');
  
  // 存储模型列表和选中的模型数据
  const [models, setModels] = useState<ModelData[]>([]);
  const [currentModel, setCurrentModel] = useState<ModelData | null>(null);
  // 存储上传的模型URL
  const [uploadedModelUrl, setUploadedModelUrl] = useState<string | null>(null);
  
  // 存储材质列表和选中的材质数据
  const [materials, setMaterials] = useState<MaterialData[]>([]);

  // 加载状态
  const [loading, setLoading] = useState<boolean>(true);
  
  // 显示加载状态和后台管理入口相关逻辑
  const showLoader = loading && (!currentModel || !currentModel.filePath);

  

  // 原始材质信息
  const [originalMaterials, setOriginalMaterials] = useState<OriginalMaterial[]>([]);
  // 当前激活的材质名称
  const [activeMaterialName, setActiveMaterialName] = useState<string | null>(null);
  // 使用ref来存储最新的激活材质名称，避免闭包陷阱
  const activeMaterialNameRef = useRef<string | null>(null);
  // 每个 mesh 对应的材质设置
  const [perMeshMaterials, setPerMeshMaterials] = useState<PerMeshMaterials>({});
  // 材质是否已经准备好
  const [materialsReady, setMaterialsReady] = useState<boolean>(false);
  // 防抖定时器

  
  // 处理模型文件上传
  const [uploadModalVisible, setUploadModalVisible] = useState(false);

  // 应用预设材质到指定的材质
  const applyPresetMaterialToActive = useCallback((material: MaterialData) => {
    // 使用ref中的值，确保获取到最新的激活材质名称
    const currentActiveMaterialName = activeMaterialNameRef.current;

    if (!currentActiveMaterialName || !originalMaterials.length) {
      return;
    }

    const targetMaterial = originalMaterials.find(m => m.name === currentActiveMaterialName);

    if (!targetMaterial || !targetMaterial.isEditable) {
      return;
    }

    const newSettings: MaterialProps = {
      color: material.data.color,
      metalness: material.data.metalness ?? 0.5,
      roughness: material.data.roughness ?? 0.5,
      opacity: material.data.opacity ?? 1,
      textureUrl: 'textureUrl' in material && typeof material.textureUrl === 'string' ? material.textureUrl : undefined // 安全访问可能存在的 textureUrl 属性
    };

    // 应用到对应的mesh
    setPerMeshMaterials(prev => ({
      ...prev,
      [targetMaterial.meshName]: newSettings
    }));
  }, [originalMaterials]);

  const handleSystemMaterialSelect = useCallback((presetMaterial: RawMaterialData) => {
    setSelectedPresetId(presetMaterial.id);
    
    // 如果是临时上传的模型，应用材质到整个模型
    if (uploadedModelUrl) {
      const newSettings: MaterialProps = {
        color: presetMaterial.data.color,
        metalness: presetMaterial.data.metalness ?? 0.5,
        roughness: presetMaterial.data.roughness ?? 0.5,
        opacity: presetMaterial.data.opacity ?? 1,
        textureUrl: 'textureUrl' in presetMaterial && typeof presetMaterial.textureUrl === 'string' ? presetMaterial.textureUrl : undefined
      };
      
      // 应用到所有材质
      const updatedMaterials: PerMeshMaterials = {};
      originalMaterials.forEach(material => {
        updatedMaterials[material.meshName] = newSettings;
      });
      setPerMeshMaterials(updatedMaterials);
      return;
    }
    
    // 系统模型的原有逻辑：确保有激活的材质
    if (!activeMaterialNameRef.current) {
      return;
    }
    
    // 立即应用材质，使用当前激活的材质名称
    applyPresetMaterialToActive(presetMaterial);
  }, [applyPresetMaterialToActive, uploadedModelUrl, originalMaterials]);

  // 删除临时上传模型
  const handleRemoveUploadedModel = useCallback(() => {
    // 释放 URL
    if (uploadedModelUrl) {
      URL.revokeObjectURL(uploadedModelUrl);
    }
    setUploadedModelUrl(null);
    
    // 清除路由状态中的 uploadedFile，防止刷新后重新加载
    navigate(location.pathname, { replace: true, state: {} });
    
    // 还原为后台模型数据的第一个（若存在）
    if (models.length > 0) {
      setSelectedModelId(models[0].id.toString());
      setCurrentModel(models[0]);
    } else {
      setSelectedModelId('');
      setCurrentModel(null);
    }
    // 重置材质状态
    setActiveMaterialName(null);
    activeMaterialNameRef.current = null;
    setOriginalMaterials([]);
    setPerMeshMaterials({});
    setMaterialsReady(false);
  }, [uploadedModelUrl, models, navigate, location.pathname]);
  // 计算默认材质设置 - 确保与当前激活材质的实际状态完全同步
  const defaultMaterialSettings = useMemo(() => {
    // 对于临时上传的模型，返回当前应用的材质参数
    if (uploadedModelUrl) {
      // 获取第一个mesh的材质参数作为默认值（因为临时模型所有mesh使用相同材质）
      const firstMeshName = originalMaterials.length > 0 ? originalMaterials[0].meshName : null;
      if (firstMeshName && perMeshMaterials[firstMeshName]) {
        const appliedMaterial = perMeshMaterials[firstMeshName];
        return {
          color: appliedMaterial.color,
          metalness: appliedMaterial.metalness,
          roughness: appliedMaterial.roughness,
          opacity: appliedMaterial.opacity,
          textureUrl: appliedMaterial.textureUrl
        };
      }
      // 如果没有应用任何材质，返回默认值
      return {
        color: 'var(--color-material-default)',
        metalness: 0.5,
        roughness: 0.5,
        opacity: 1,
        textureUrl: undefined
      };
    }

    // 系统模型的原有逻辑
    if (!activeMaterialName) return null;

    const activeOriginal = originalMaterials.find(m => m.name === activeMaterialName);
    if (!activeOriginal) return null;

    // 获取当前激活材质球的实际应用状态
    const currentAppliedMaterial = perMeshMaterials[activeOriginal.meshName];
    
    // 优先使用当前已应用的材质参数（无论是自定义还是系统材质应用的结果）
    if (currentAppliedMaterial) {
      return {
        color: currentAppliedMaterial.color,
        metalness: currentAppliedMaterial.metalness,
        roughness: currentAppliedMaterial.roughness,
        opacity: currentAppliedMaterial.opacity,
        textureUrl: currentAppliedMaterial.textureUrl
      };
    }

    // 如果没有应用任何材质，使用原始材质参数作为默认值
    return {
      color: activeOriginal.color || 'var(--color-material-default)',
      metalness: activeOriginal.metalness || 0.5,
      roughness: activeOriginal.roughness || 0.5,
      opacity: activeOriginal.opacity || 1,
      textureUrl: undefined
    };
  }, [uploadedModelUrl, activeMaterialName, originalMaterials, perMeshMaterials]);

  const handleModelUpload = useCallback((file: File) => {
    // 验证文件
    const validation = validateFile(file, {
      ...FILE_VALIDATION_CONFIGS.MODEL,
      allowedExtensions: [...FILE_VALIDATION_CONFIGS.MODEL.allowedExtensions]
    });
    if (!validation.isValid) {
      notify(validation.error!, 'error');
      return;
    }

    try {
      // 清理之前的上传模型URL
      if (uploadedModelUrl) {
        URL.revokeObjectURL(uploadedModelUrl);
      }
      
      // 清理所有相关状态和缓存
      clearTextureCache();
      
      const url = URL.createObjectURL(file);
      setUploadedModelUrl(url);
      const tempModel: ModelData = {
        id: -1,
        name: file.name.replace(/\.[^/.]+$/, ''),
        filePath: url,
        thumbnailPath: null,
        fileType: file.name.split('.').pop()?.toUpperCase() || null,
        size: file.size,
        createdAt: new Date().toISOString()
      };

      // 完全重置所有状态
      setCurrentModel(tempModel);
      setActiveMaterialName(null);
      activeMaterialNameRef.current = null;
      setOriginalMaterials([]);
      setPerMeshMaterials({});
      setMaterialsReady(false);
      setSelectedPresetId(null);
      setUploadModalVisible(false);
      
      // 强制垃圾回收
      if (window.gc) {
        window.gc();
      }
      
      // 只显示一次上传成功提示

    } catch (error) {
      console.error('文件处理失败:', error);
      notify('文件处理失败，请重试', 'error');
    }
  }, [notify, uploadedModelUrl]);

  // 当从路由 state 接收到上传的文件时，立即加载
  useEffect(() => {
    const state = location.state as { uploadedFile?: File } | null;
    if (state?.uploadedFile) {
      handleModelUpload(state.uploadedFile);
    }
    // 仅在首次渲染时检查一次，避免重复处理
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 处理原始材质信息提取
  const handleOriginalMaterialsExtracted = useCallback((materials: OriginalMaterial[]) => {
    setOriginalMaterials(materials);
    setMaterialsReady(true);
    // 如果有材质，默认激活第一个可编辑材质
    const editableMaterials = materials.filter(m => m.isEditable);
    if (editableMaterials.length > 0) {
      const defaultMaterialName = editableMaterials[0].name;
      setActiveMaterialName(defaultMaterialName);
      activeMaterialNameRef.current = defaultMaterialName;
    }
  }, []);

  // 处理材质激活
  const handleMaterialActivate = useCallback((materialName: string) => {
    // 如果激活的是同一个材质，不需要清空 selectedPresetId
    const isSameMaterial = activeMaterialNameRef.current === materialName;
    
    // 同时更新state和ref
    setActiveMaterialName(materialName);
    activeMaterialNameRef.current = materialName;

    // 只有当切换到不同的材质时，才清空当前选中的系统预设材质
    if (!isSameMaterial) {
      setSelectedPresetId(null);
    }
  }, [setActiveMaterialName, setSelectedPresetId]);

  // 处理自定义材质变化
  const handleCustomMaterialChange = useCallback((settings: { color: string; metalness: number; roughness: number; opacity: number; textureUrl?: string }) => {
    // 用户开始自定义材质时，取消会通材料的高亮
    setSelectedPresetId(null);

    const newSettings = {
      color: settings.color,
      metalness: settings.metalness,
      roughness: settings.roughness,
      opacity: settings.opacity,
      textureUrl: settings.textureUrl
    };

    // 如果是临时上传的模型，应用材质到整个模型
    if (uploadedModelUrl) {
      const updatedMaterials: PerMeshMaterials = {};
      originalMaterials.forEach(material => {
        updatedMaterials[material.meshName] = newSettings;
      });
      setPerMeshMaterials(updatedMaterials);
      return;
    }

    // 系统模型的原有逻辑：只应用到激活的材质
    if (!activeMaterialNameRef.current) return;

    const activeMaterial = originalMaterials.find(m => m.name === activeMaterialNameRef.current);
    if (!activeMaterial || !activeMaterial.isEditable) return;

    // 应用到对应的mesh
    setPerMeshMaterials(prev => ({
      ...prev,
      [activeMaterial.meshName]: newSettings
    }));
  }, [originalMaterials, setSelectedPresetId, setPerMeshMaterials, uploadedModelUrl]);

  useEffect(() => {
    const fetchData = async () => {
      // 如果有临时上传的模型，则不执行fetchData
      if (uploadedModelUrl) {
        return;
      }
      setLoading(true);
      try {
        // 获取模型数据
        const modelData = await apiService.getModels();
        setModels(modelData);

        // 预加载模型文件以提升性能
        modelData.forEach(model => {
          if (model.filePath) {
            useGLTF.preload(model.filePath);
          }
        });

        // 优先使用URL参数中的modelId，否则使用第一个模型
        if (modelId && modelData.length > 0) {
          // Convert modelId to number for comparison
          const targetModelId = parseInt(modelId, 10);
          const found = modelData.find(m => m.id === targetModelId);
          if (found) {
            setSelectedModelId(modelId);
            setCurrentModel(found);
          } else {
            // 如果指定的模型不存在，使用第一个模型
            setSelectedModelId(String(modelData[0].id));
            setCurrentModel(modelData[0]);
          }
        } else if (modelData.length > 0) {
          // 如果没有指定modelId或modelId为空，使用第一个模型
          setSelectedModelId(String(modelData[0].id));
          setCurrentModel(modelData[0]);
        }

        // 获取材质数据
        const materialData = await apiService.getMaterials();
        setMaterials(materialData);
      } catch (error) {
        console.error('获取数据失败:', error);
        notify('数据加载失败，请检查网络连接', 'error');
      } finally {
        setLoading(false);
      }
    };
    fetchData();
    // 依赖modelId、notify和uploadedModelUrl的变化
   
  }, [modelId, notify, uploadedModelUrl]);
  
  useEffect(() => {
    if (!selectedModelId || models.length === 0) {
      setCurrentModel(null);
      return;
    }

    const newModel = models.find(m => m.id === parseInt(selectedModelId, 10));

    setCurrentModel(prevModel => {
      // 如果模型发生了变化，清理之前的资源
      if (prevModel && newModel && prevModel.id !== newModel.id) {
        console.log(`Model changed from ${prevModel.id} to ${newModel.id}, clearing previous resources`);
        
        // 在切换模型前，主动释放所有WebGL资源
        if (rendererRef.current) {
          // 强制完成所有待处理的渲染
          rendererRef.current.renderLists.dispose();
        }
        
        // 清理之前模型的缓存
        if (prevModel.filePath) {
          try {
            useGLTF.clear(prevModel.filePath);
          } catch (e) {
            console.warn('Error clearing GLTF cache:', e);
          }
        }
        
        // 清理纹理缓存
        clearTextureCache();
        
        // 延迟一帧再进行垃圾回收，确保所有资源都被正确释放
        setTimeout(() => {
          // 强制垃圾回收
          if (window.gc) {
            window.gc();
          }
        }, 0);
      }
      return newModel || null;
    });
  }, [selectedModelId, models]);
  



  // 组件卸载时的清理逻辑
  useEffect(() => {
    return () => {
      // 清理上传的模型URL
      if (uploadedModelUrl) {
        URL.revokeObjectURL(uploadedModelUrl);
      }
      
      // 清理所有材质
       Object.values(perMeshMaterials).forEach(material => {
         if (material && material instanceof THREE.Material) {
           material.dispose();
         }
       });
      
      // 清理纹理缓存
      clearTextureCache();
      
      // 强制垃圾回收
      if (window.gc) {
        window.gc();
      }
    };
  }, [uploadedModelUrl, perMeshMaterials]);

  // -------------- Canvas 引用及复制 / 保存功能 --------------
  const canvasRef = useRef<HTMLCanvasElement | null>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);

  const handleCopyImage = useCallback(async () => {
    try {
      const canvas = canvasRef.current;
      if (!canvas) return;
      
      // 定义 Clipboard 接口的类型声明
      interface ExtendedClipboard extends Clipboard {
        write(data: ClipboardItem[]): Promise<void>;
      }
      
      interface ClipboardItemConstructor {
        new (items: Record<string, Blob>): ClipboardItem;
      }
      
      canvas.toBlob(async (blob) => {
        if (!blob) return;
        
        try {
          // 使用类型断言处理实验性 API
          const clipboard = navigator.clipboard as unknown as ExtendedClipboard;
          const ClipboardItem = window.ClipboardItem as unknown as ClipboardItemConstructor;
          
          if (!ClipboardItem) {
            throw new Error('Clipboard API 不支持 ClipboardItem');
          }
          
          const item = new ClipboardItem({ 'image/png': blob });
          await clipboard.write([item]);
          notify('图片已复制到剪贴板', 'success');
        } catch (err) {
          console.error('复制图片失败', err);
          notify('复制图片失败，请重试', 'error');
        }
      }, 'image/png');
    } catch (error) {
      console.error('复制失败:', error);
      notify('复制失败，请检查浏览器权限', 'error');
    }
  }, [notify]);

  // 保存当前渲染结果到本地
  const handleSaveImage = useCallback(() => {
    try {
      const canvas = canvasRef.current;
      if (!canvas) {
        notify('无法获取渲染画布', 'error');
        return;
      }

      canvas.toBlob((blob) => {
        if (!blob) {
          notify('图片生成失败', 'error');
          return;
        }

        try {
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `render-${new Date().getTime()}.png`;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
          notify('图片保存成功', 'success');
        } catch (error) {
          console.error('保存图片失败:', error);
          notify('保存图片失败', 'error');
        }
      }, 'image/png');
    } catch (error) {
      console.error('保存图片失败:', error);
      notify('保存图片失败', 'error');
    }
  }, [notify]);
  // --------------------------------------------------------------

  return (
    <div className="render-page">

      <div className="title-bar">
        <div className="title-bar__left">
          <img
            className="logo logo--clickable"
            src={LogoImage}
            alt="RINKO"
            onClick={handleLogoClick}
          />
        </div>
        <div className="user-controls">
          <SecondaryButton icon={Copy} onClick={handleCopyImage}>复制图片</SecondaryButton>
          <PrimaryButton icon={Download} onClick={handleSaveImage}>保存图片</PrimaryButton>
        </div>
      </div>

      <div className="render-container">
        <div className="render-window">
          {/* 3D渲染区域 */}
          <div className="render-area">
              <Canvas
                onCreated={({ gl }) => {
                  canvasRef.current = gl.domElement;
                  rendererRef.current = gl;

                  try {
                    // 注册WebGL上下文到管理器
                    registerWebGLContext(gl);

                    // 优化WebGL渲染器配置
                    gl.outputColorSpace = THREE.SRGBColorSpace;
                    gl.toneMapping = THREE.ACESFilmicToneMapping;
                    gl.toneMappingExposure = 1;
                    gl.shadowMap.enabled = true;
                    gl.shadowMap.type = THREE.PCFSoftShadowMap;

                    // 设置像素比以提高性能
                    const pixelRatio = Math.min(window.devicePixelRatio, 1.5); // 降低最大像素比
                    gl.setPixelRatio(pixelRatio);
                    
                    // 设置合理的缓冲区大小
                    gl.info.autoReset = true;
                    
                    // 监听上下文丢失事件
                    const canvas = gl.domElement;
                    
                    // 使用命名函数，以便后续可以正确移除事件监听
                    const handleContextLost = (event: Event) => {
                      event.preventDefault();
                      console.warn('WebGL context lost, attempting to restore...');
                      console.warn('WebGL context lost, attempting to restore...');
                    };

                    const handleContextRestored = () => {
                      console.log('WebGL context restored');
                      console.log('WebGL context restored');
                      // 强制重新渲染
                      if (currentModel) {
                        setPerMeshMaterials(prev => ({ ...prev }));
                      }
                    };
                    
                    // 存储事件处理函数引用到gl对象上
                    (gl as unknown as { __mainContextLostHandler?: EventListener; __mainContextRestoredHandler?: EventListener }).__mainContextLostHandler = handleContextLost;
                    (gl as unknown as { __mainContextLostHandler?: EventListener; __mainContextRestoredHandler?: EventListener }).__mainContextRestoredHandler = handleContextRestored;

                    canvas.addEventListener('webglcontextlost', handleContextLost);
                    canvas.addEventListener('webglcontextrestored', handleContextRestored);

                    return () => {
                      // 移除事件监听
                      const glWithHandlers = gl as unknown as { __mainContextLostHandler?: EventListener; __mainContextRestoredHandler?: EventListener };
                      if (glWithHandlers.__mainContextLostHandler) {
                        canvas.removeEventListener('webglcontextlost', glWithHandlers.__mainContextLostHandler);
                      }
                      if (glWithHandlers.__mainContextRestoredHandler) {
                        canvas.removeEventListener('webglcontextrestored', glWithHandlers.__mainContextRestoredHandler);
                      }
                      
                      // 安全释放上下文
                      safeReleaseContext(gl);
                    };
                  } catch (error) {
                    console.error('Error setting up main WebGL context:', error);
                    return null;
                  }
                }}
              gl={{
                preserveDrawingBuffer: true, // 修复图片复制保存功能
                alpha: true,
                antialias: false,
                powerPreference: 'low-power', // 改为低功耗模式，减少GPU负担
                failIfMajorPerformanceCaveat: false,
                stencil: false,
                depth: true,
                premultipliedAlpha: false,
                logarithmicDepthBuffer: false
              }}
              onError={(error) => {
                console.error('Canvas error:', error);
                notify('渲染引擎出现错误，请刷新页面重试', 'error');
              }}
              shadows
              camera={{ position: [0, 1, 4], fov: 45 }}
className="transparent-bg"
              dpr={[1, 2]}>
              
              {/* 将Stage移出条件渲染 */}
              {!showLoader && currentModel && (uploadedModelUrl || currentModel.filePath) && (
                <ErrorBoundary 
                  fallback={
                    <Html center>
                      <StatusMessage
                        type="error"
                        message="模型加载失败，请尝试刷新页面或选择其他模型"
                        description="请检查模型文件格式或网络连接"
                        size="small"
                      />
                    </Html>
                  }
                  onError={(error) => {
                    console.error('Model rendering error:', error);
                    // 清理可能损坏的状态
                    clearTextureCache();
                    if (window.gc) {
                      window.gc();
                    }
                  }}
                >
                  <Stage environment="city" intensity={0.6} adjustCamera={false} shadows={false} preset="rembrandt" scale={1}>
                    <Model
                      key={`${currentModel.id}-${uploadedModelUrl || currentModel.filePath}-${selectedModelId}`}
                      modelPath={uploadedModelUrl || currentModel.filePath || ''}
                      perMeshMaterials={perMeshMaterials}
                      activeMaterialName={activeMaterialName}
                      onOriginalMaterialsExtracted={handleOriginalMaterialsExtracted}
                    />
                  </Stage>
                </ErrorBoundary>
              )}
              
              <Suspense fallback={<Loader />}>
                {showLoader ? (
                  <Loader />
                ) : !currentModel || !(uploadedModelUrl || currentModel.filePath) ? (
                  <Html center>
                    <StatusMessage
                      type="info"
                      message="未选择模型或模型数据缺失"
                      size="small"
                    />
                  </Html>
                ) : null}
              </Suspense>
              <Environment preset="city" />
              <OrbitControls 
                ref={controlsRef}
                makeDefault 
                enablePan={true} 
                enableZoom={true} 
                enableRotate={true} 
              />
            </Canvas>
          </div>
          
          <div className="button-container">
            <div
              className="control-button control-button--clickable"
              onClick={resetView}
            >
              <div className="icon-wrapper">
                <RotateCcw size={16} />
              </div>
              <span>默认视图</span>
            </div>
            <Tooltip 
              content={
                <div className="tooltip-help-content">
                                    <div className="help-item"><RotateCw size={14} /><span>左键：旋转视图</span></div>
                  <div className="help-item"><Move size={14} /><span>右键：平移视图</span></div>
                                    <div className="help-item"><ZoomIn size={14} /><span>滚轮：缩放视图</span></div>
                </div>
              }
              position="top"
            >
              <div className="control-button">
                <div className="icon-wrapper">
                  <HelpCircle size={16} />
                </div>
                <span>操作说明</span>
              </div>
            </Tooltip>
          </div>
        </div>
        
        {/* 属性面板 */}
        <div className="property-panel">
          <div className="panel-section">
            <div className="section-header">
              <div className="icon-wrapper">
                <Box size={16} />
              </div>
              <span>模型</span>
            </div>
            {/* 如果存在临时上传模型，则展示模型卡片和删除按钮；否则展示下拉和上传按钮 */}
            <div className="dropdown-wrapper">
              {uploadedModelUrl ? (
                <div className="uploaded-model-container">
                  <div className="uploaded-model-name">
                    {currentModel?.name?.replace(/\.[^/.]+$/, '') || '未命名模型'}
                  </div>
                  <IconButton
                    icon={Trash2}
                    onClick={handleRemoveUploadedModel}
                    variant="danger"
                    size="small"
                  />
                </div>
              ) : (
                <>
                  <DropDown
                      options={models.map(model => ({ value: model.id.toString(), label: model.name, thumbnail: model.thumbnailPath ?? undefined }))}
                      value={selectedModelId}
                      listClassName="drop-down__options--grid"
                      onChange={(value) => {
                        const id = value as string;
                        if (id !== selectedModelId) {
                          console.log('Switching model from', selectedModelId, 'to', id);
                          navigate(`/render/${id}`);
                          
                          // 重置相关状态
                          setActiveMaterialName(null);
                          activeMaterialNameRef.current = null;
                          setOriginalMaterials([]);
                          setPerMeshMaterials({});
                          setMaterialsReady(false);
                          setSelectedPresetId(null);
                        }
                      }}
                      placeholder={models.length === 0 ? '暂无模型' : ''}
                    />
                  <div className="upload-button-wrapper">
                    <SecondaryButton
                      icon={Upload}
                      fullWidth
                      onClick={() => setUploadModalVisible(true)}
                    >
                      上传模型
                    </SecondaryButton>
                  </div>
                </>
              )}
            </div>
          </div>
          
          {/* 材质设置面板 */}
          {currentModel && (
            <div className="panel-section panel-section--flex">
              <div className="section-header">
                <div className="icon-wrapper">
                  <Paintbrush size={16} />
                </div>
                <span>材质设置</span>
              </div>
            
            {/* 可编辑材质列表 - 仅系统模型显示 */}
            {!uploadedModelUrl && (
              <div className="editable-materials-grid">
                {!materialsReady ? (
                  <EmptyState
                    title="材质加载中..."
                    size="small"
                  />
                ) : originalMaterials.filter(material => material.isEditable).length > 0 ? (
                  <MaterialsGrid variant="default">
                    {originalMaterials
                      .filter(material => material.isEditable)
                      .map((material) => {
                        // 获取当前应用的材质设置，如果有的话
                        const appliedMaterial = perMeshMaterials[material.meshName];

                        // 创建一个临时的MaterialData对象用于MaterialThumbnail
                        // 如果有应用的材质，使用应用的材质属性；否则使用原始材质属性
                        const materialData: MaterialData = {
                          id: `original-${material.name}`,
                          model_id: 0,
                          name: material.name,
                          thumbnailPath: null,
                          data: {
                            color: appliedMaterial ? appliedMaterial.color : material.color,
                            metalness: appliedMaterial ? appliedMaterial.metalness : material.metalness,
                            roughness: appliedMaterial ? appliedMaterial.roughness : material.roughness,
                            glass: appliedMaterial ? (1 - appliedMaterial.opacity) : (1 - material.opacity)
                          },
                          createdAt: ''
                        };

                        return (
                          <MaterialThumbnail
                            key={`${material.name}-${material.meshName}-${JSON.stringify(appliedMaterial || {})}`}
                            material={materialData}
                            active={material.name === activeMaterialName}
                            onClick={() => handleMaterialActivate(material.name)}
                            textureUrl={appliedMaterial?.textureUrl}
                          />
                        );
                      })}
                  </MaterialsGrid>
                ) : (
                  <EmptyState
                    title="暂无可编辑材质"
                    description="当前模型没有可编辑的材质"
                    size="small"
                  />
                )}
              </div>
            )}
            
            {materialsReady && (
              <>
                <TabGroup
                  className="tab-switch"
                  gap={4}
                  defaultActiveIndex={activeTab === '会通材料' ? 0 : 1}
                  onChange={(index) => setActiveTab(index === 0 ? '会通材料' : '自定义')}
                >
                  <TabItem label="会通材料" className="tab-item" />
                  <TabItem label="自定义" className="tab-item" />
                </TabGroup>

                <div className="materials-container">
                  <div className={activeTab === '会通材料' ? 'tab-content' : 'tab-content--hidden'}>
                    <div className="search-wrapper">
                      <SearchBox
                        placeholder="搜索"
                        value={searchQuery}
                        onChange={(value) => setSearchQuery(value)}
                        onSearch={() => {}}
                      />
                    </div>

                    <MaterialsGrid variant="preset">
                      {materials
                        .filter(material => material.name.includes(searchQuery))
                        .map((material) => (
                          <MaterialThumbnail
                            key={material.id}
                            material={material}
                            active={material.id === selectedPresetId}
                            size="preset"
                            onClick={() => handleSystemMaterialSelect(material)}
                          />
                        ))}
                    </MaterialsGrid>
                  </div>
                  
                  <div className={activeTab === '自定义' ? 'tab-content' : 'tab-content--hidden'}>
                    {/* 临时上传模型或有可编辑材质时显示自定义面板 */}
                    {(uploadedModelUrl || (activeMaterialName && originalMaterials.find(m => m.name === activeMaterialName)?.isEditable)) ? (
                      <CustomMaterialPanel
                        defaultSettings={defaultMaterialSettings}
                        onChange={handleCustomMaterialChange}
                      />
                    ) : (
                      <EmptyState
                        title={activeMaterialName ? '当前材质不可编辑' : '请先选择一个可编辑的材质'}
                        description={activeMaterialName ? '此材质为系统预设材质，无法修改' : '从上方材质列表中选择一个可编辑的材质'}
                        size="small"
                      />
                    )}
                  </div>
                </div>
              </>
            )}
            </div>
          )}
        </div>
      </div>
      {uploadModalVisible && (
        <UploadModal
          visible={uploadModalVisible}
          onClose={() => setUploadModalVisible(false)}
          onFileSelect={handleModelUpload}
          validationOptions={FILE_VALIDATION_CONFIGS.GLB_ONLY}
          accept=".glb,.gltf"
          title="上传模型"
          placeholder="点击或拖拽模型文件到此处上传"
          formatHint="支持格式: GLB、GLTF"
          selectedFileName={undefined}
          selectedFileSize={undefined}
        />
      )}

    </div>
  );
};

export default RenderPage;