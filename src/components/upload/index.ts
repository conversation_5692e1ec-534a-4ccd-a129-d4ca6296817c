// 原始上传组件（保持向后兼容）
export { Upload } from './upload';
export type { UploadProps } from './upload';
export {
  FileUpload as LegacyFileUpload,
  ImageUpload as LegacyImageUpload,
  TextureUpload as LegacyTextureUpload,
  ModelUpload as LegacyModelUpload
} from './upload';

// 增强版上传组件（推荐使用）
export { EnhancedUpload } from './enhanced-upload';
export type { EnhancedUploadProps } from './enhanced-upload';
export {
  FileUpload,
  ImageUpload,
  TextureUpload,
  ModelUpload,
  UploadModal
} from './enhanced-upload';
export type {
  UploadVariant,
  UploadSize,
  UploadMode
} from './enhanced-upload';