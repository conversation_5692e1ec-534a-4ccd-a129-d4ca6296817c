/* 统一上传组件样式 - 仅保留基础结构 */
.upload {
  width: 100%;
}

/* 当有预览内容时，移除padding */
.upload__area:has(.upload__preview-container),
.upload__area:has(.upload__texture-preview) {
  padding: 0;
}

/* 当只有占位符时，保留padding */
.upload__area:has(.upload__placeholder) {
  padding: var(--spacing-base);
}

.upload__area:hover {
  background-color: var(--color-bg-subtle);
}

/* 固定尺寸约束 - 防止内容撑开容器 */
.upload__area--texture {
  min-height: 120px;
  max-height: 120px;
  height: 120px;
}

.upload__area--image {
  width: 120px;
  height: 120px;
  min-width: 120px;
  min-height: 120px;
  max-width: 120px;
  max-height: 120px;
}

.upload__area--small {
  height: 100px;
  min-height: 100px;
  max-height: 100px;
}

.upload__area--medium {
  height: 140px;
  min-height: 140px;
  max-height: 140px;
}

.upload__area--large {
  height: 180px;
  min-height: 180px;
  max-height: 180px;
}

.upload__area--image.upload__area--small {
  width: 80px;
  height: 80px;
  min-width: 80px;
  min-height: 80px;
  max-width: 80px;
  max-height: 80px;
}

.upload__area--image.upload__area--large {
  width: 160px;
  height: 160px;
  min-width: 160px;
  min-height: 160px;
  max-width: 160px;
  max-height: 160px;
}

/* 占位符内容 - 仅保留基础结构 */
.upload__placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  width: 100%;
}

.upload__placeholder-text {
  font-weight: 500;
}

/* 上传图标样式 */
.upload__icon {
  color: var(--color-content-mute);
}

.upload__icon--model {
  width: var(--spacing-huge);
  height: var(--spacing-huge);
}

.upload__icon--texture,
.upload__icon--image {
  width: var(--spacing-xxl);
  height: var(--spacing-xxl);
}

.upload__format-hint {
  opacity: 0.6;
  font-size: var(--font-size-sm);
  color: var(--color-text-subtle);
}

.upload__selected-file {
  opacity: 0.7;
  font-size: var(--font-size-sm);
  color: var(--color-text-subtle);
}

/* 预览相关样式 - 固定尺寸约束 */
.upload__preview-container {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  padding: 0;
  margin: 0;
}

.upload__preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  border: none;
  outline: none;
}

.upload__texture-preview {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  padding: 0;
  margin: 0;
}

.upload__texture-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  border: none;
  outline: none;
}

/* 移除按钮 - 定位到右上角 */
.upload__remove-button {
  position: absolute;
  top: 4px;
  right: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  z-index: 10;
}

/* IconButton在预览容器中的定位样式 */
.upload__preview-container .icon-button,
.upload__texture-preview .icon-button {
  position: absolute;
  top: 4px;
  right: 4px;
  z-index: 10;
}

/* 图片预览中的按钮hover效果 */
.upload__preview-container .icon-button:hover:not(:disabled) {
  transform: scale(1.1);
  transition: all 0.2s ease;
}

/* 图片预览和纹理预览中的X按钮背景样式 */
.upload__preview-container .icon-button,
.upload__texture-preview .icon-button {
  background: rgba(0, 0, 0, 0.6);
  border-radius: 4px;
  backdrop-filter: blur(4px);
}

/* IconButton在文件信息中的样式 */
.upload__file-info .icon-button {
  flex-shrink: 0;
}

/* 文件信息显示状态 */
.upload__file-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  width: 100%;
  padding: var(--spacing-sm);
  background-color: var(--color-bg-subtle);
  border-radius: var(--radius-sm);
}

.upload__file-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background-color: var(--color-primary);
  color: white;
  border-radius: var(--radius-sm);
  flex-shrink: 0;
}

.upload__file-details {
  flex: 1;
  min-width: 0;
}

.upload__file-name {
  font-weight: 500;
  font-size: var(--font-size-sm);
  color: var(--color-text);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 2px;
}

.upload__file-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-sm);
  color: var(--color-text-subtle);
}

.upload__file-format {
  font-weight: 500;
  color: var(--color-primary);
}

.upload__file-separator {
  opacity: 0.5;
}

.upload__file-size {
  opacity: 0.8;
}

/* 隐藏的文件输入 */
.upload__input {
  display: none;
}