import React, { useRef, useState, type ReactNode } from 'react';
import { Upload as UploadIcon, Plus, X } from 'lucide-react';
import { createDragUploadHandlers, handleFileInputChange, type FileValidationOptions } from '../../utils/fileUpload';
import { IconButton } from '../icon-button/icon-button';
import './upload.css';

export type UploadVariant = 'file' | 'image' | 'texture' | 'model';
export type UploadSize = 'small' | 'medium' | 'large';

interface BaseUploadProps {
  /** 文件选择回调 */
  onFileSelect: (file: File) => void;
  /** 验证配置 */
  validationOptions: FileValidationOptions;
  /** 错误处理回调 */
  onError?: (error: string) => void;
  /** 接受的文件类型 */
  accept?: string;
  /** 是否禁用 */
  disabled?: boolean;
  /** 自定义类名 */
  className?: string;
  /** 组件大小 */
  size?: UploadSize;
}

interface FileUploadProps extends BaseUploadProps {
  variant: 'file' | 'model';
  /** 显示的提示文本 */
  placeholder?: string;
  /** 格式提示文本 */
  formatHint?: string;
  /** 已选择的文件名 */
  selectedFileName?: string;
  /** 已选择的文件大小（字节） */
  selectedFileSize?: number;
  /** 自定义图标 */
  icon?: ReactNode;
  /** 是否显示移除按钮 */
  showRemoveButton?: boolean;
  /** 移除回调 */
  onRemove?: () => void;
}

interface ImageUploadProps extends BaseUploadProps {
  variant: 'image';
  /** 预览图片URL */
  previewUrl?: string | null;
  /** 占位符文本 */
  placeholder?: string;
  /** 是否显示移除按钮 */
  showRemoveButton?: boolean;
  /** 移除回调 */
  onRemove?: () => void;
}

interface TextureUploadProps extends BaseUploadProps {
  variant: 'texture';
  /** 预览图片URL */
  previewUrl?: string | null;
  /** 占位符文本 */
  placeholder?: string;
  /** 是否显示移除按钮 */
  showRemoveButton?: boolean;
  /** 移除回调 */
  onRemove?: () => void;
}

export type UploadProps = FileUploadProps | ImageUploadProps | TextureUploadProps;

export const Upload: React.FC<UploadProps> = (props) => {
  const {
    onFileSelect,
    validationOptions,
    onError,
    accept,
    disabled = false,
    className = '',
    size = 'medium',
    variant
  } = props;

  const inputRef = useRef<HTMLInputElement | null>(null);
  const [dragOver, setDragOver] = useState(false);

  const handleFileSelectInternal = (file: File) => {
    onFileSelect(file);
  };

  const dragHandlers = createDragUploadHandlers(
    handleFileSelectInternal,
    setDragOver,
    validationOptions,
    onError || ((error) => alert(error))
  );

  const handleClick = () => {
    if (!disabled) {
      inputRef.current?.click();
    }
  };

  const renderContent = () => {
    switch (variant) {
      case 'image':
        return renderImageContent(props as ImageUploadProps);
      case 'texture':
        return renderTextureContent(props as TextureUploadProps);
      case 'file':
      case 'model':
      default:
        return renderFileContent(props as FileUploadProps);
    }
  };

  const renderImageContent = (imageProps: ImageUploadProps) => {
    const { previewUrl, placeholder = '点击上传图片', showRemoveButton, onRemove } = imageProps;
    
    return (
      <>
        {previewUrl ? (
          <div className="upload__preview-container">
            <img 
              src={previewUrl} 
              alt="预览图" 
              className="upload__preview-image" 
            />
            {showRemoveButton && onRemove && (
              <IconButton
                icon={X}
                onClick={(e) => {
                  e.stopPropagation();
                  // 重置input的value，确保删除后可以重新选择相同文件
                  if (inputRef.current) {
                    inputRef.current.value = '';
                  }
                  onRemove();
                }}
                variant="upload"
                size="small"
              />
            )}
          </div>
        ) : (
          <div className="upload__placeholder">
            <Plus size={variant === 'image' ? 24 : 20} />
            <span className="upload__placeholder-text">{placeholder}</span>
          </div>
        )}
      </>
    );
  };

  const renderTextureContent = (textureProps: TextureUploadProps) => {
    const { previewUrl, placeholder = '点击或拖拽上传纹理', showRemoveButton, onRemove } = textureProps;
    
    return (
      <>
        {previewUrl ? (
          <div className="upload__texture-preview">
            <img src={previewUrl} alt="纹理预览" className="upload__texture-image" />
            {showRemoveButton && onRemove && (
              <IconButton
                icon={X}
                onClick={(e) => {
                  e.stopPropagation();
                  // 重置input的value，确保删除后可以重新选择相同文件
                  if (inputRef.current) {
                    inputRef.current.value = '';
                  }
                  onRemove();
                }}
                variant="upload"
                size="small"
              />
            )}
          </div>
        ) : (
          <div className="upload__placeholder">
            <UploadIcon size={24} />
            <span className="upload__placeholder-text">{placeholder}</span>
            <small className="upload__format-hint">支持 JPG, PNG, WebP 格式</small>
          </div>
        )}
      </>
    );
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileExtension = (fileName: string): string => {
    return fileName.split('.').pop()?.toUpperCase() || '';
  };

  const renderFileContent = (fileProps: FileUploadProps) => {
    const {
      placeholder = '点击或拖拽文件到此处上传',
      formatHint,
      selectedFileName,
      selectedFileSize,
      icon,
      showRemoveButton,
      onRemove
    } = fileProps;

    // 如果有选中的文件，显示文件信息状态
    if (selectedFileName) {
      return (
        <div className="upload__file-info">
            {icon && <div className="upload__file-icon">{icon}</div>}
            <div className="upload__file-details">
              <span className="upload__file-name">{selectedFileName}</span>
              <div className="upload__file-meta">
                <span className="upload__file-format">{getFileExtension(selectedFileName)}</span>
                <span className="upload__file-separator">•</span>
                <span className="upload__file-size">{formatFileSize(selectedFileSize || 0)}</span>
              </div>
            </div>
          {showRemoveButton && onRemove && (
            <IconButton
              icon={X}
              onClick={(e) => {
                e.stopPropagation();
                // 重置input的value，确保删除后可以重新选择相同文件
                if (inputRef.current) {
                  inputRef.current.value = '';
                }
                onRemove();
              }}
              variant="danger"
              size="small"
            />
          )}
        </div>
      );
    }

    // 默认占位符状态
    return (
      <div className="upload__placeholder">
        {icon || <UploadIcon className={`upload__icon upload__icon--${variant}`} />}
        <p className="upload__placeholder-text">{placeholder}</p>
        {formatHint && (
          <p className="upload__format-hint">{formatHint}</p>
        )}
      </div>
    );
  };

  const getUploadAreaClass = () => {
    const baseClass = 'upload__area';
    const variantClass = `upload__area--${variant}`;
    const sizeClass = `upload__area--${size}`;
    const dragClass = dragOver ? 'upload__area--drag-over' : '';
    const disabledClass = disabled ? 'upload__area--disabled' : '';
    
    return [baseClass, variantClass, sizeClass, dragClass, disabledClass]
      .filter(Boolean)
      .join(' ');
  };

  return (
    <div className={`upload ${className}`}>
      <div
        className={getUploadAreaClass()}
        onDrop={disabled ? undefined : dragHandlers.onDrop}
        onDragOver={disabled ? undefined : dragHandlers.onDragOver}
        onDragLeave={disabled ? undefined : dragHandlers.onDragLeave}
        onClick={handleClick}
      >
        {renderContent()}
      </div>

      <input
        ref={inputRef}
        type="file"
        accept={accept}
        className="upload__input"
        disabled={disabled}
        onChange={(e) => {
          if (!disabled) {
            handleFileInputChange(
              e,
              handleFileSelectInternal,
              validationOptions,
              onError || ((error) => alert(error))
            );
          }
        }}
      />
    </div>
  );
};

// 便捷组件
export const FileUpload: React.FC<Omit<FileUploadProps, 'variant'>> = (props) => (
  <Upload {...props} variant="file" />
);

export const ImageUpload: React.FC<Omit<ImageUploadProps, 'variant'>> = (props) => (
  <Upload {...props} variant="image" />
);

export const TextureUpload: React.FC<Omit<TextureUploadProps, 'variant'>> = (props) => (
  <Upload {...props} variant="texture" />
);

export const ModelUpload: React.FC<Omit<FileUploadProps, 'variant'>> = (props) => (
  <Upload {...props} variant="model" />
);