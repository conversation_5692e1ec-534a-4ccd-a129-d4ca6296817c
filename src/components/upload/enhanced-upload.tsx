import React, { useRef, useState, type ReactNode } from 'react';
import { Upload as UploadIcon, Plus, X } from 'lucide-react';
import { IconButton } from '../icon-button/icon-button';
import { createDragUploadHandlers, handleFileInputChange, type FileValidationOptions } from '../../utils/fileUpload';
import { Modal } from '../modal/modal';
import './enhanced-upload.css';

export type UploadVariant = 'file' | 'image' | 'texture' | 'model';
export type UploadSize = 'small' | 'medium' | 'large';
export type UploadMode = 'inline' | 'modal';

interface BaseUploadProps {
  /** 文件选择回调 */
  onFileSelect: (file: File) => void;
  /** 验证配置 */
  validationOptions: FileValidationOptions;
  /** 错误处理回调 */
  onError?: (error: string) => void;
  /** 接受的文件类型 */
  accept?: string;
  /** 是否禁用 */
  disabled?: boolean;
  /** 自定义类名 */
  className?: string;
  /** 组件大小 */
  size?: UploadSize;
  /** 显示模式 */
  mode?: UploadMode;
  /** 模态框标题（仅在 modal 模式下有效） */
  modalTitle?: string;
  /** 模态框描述（仅在 modal 模式下有效） */
  modalDescription?: string;
  /** 模态框可见性控制（仅在 modal 模式下有效） */
  modalVisible?: boolean;
  /** 模态框关闭回调（仅在 modal 模式下有效） */
  onModalClose?: () => void;
}

interface FileUploadProps extends BaseUploadProps {
  variant: 'file' | 'model';
  /** 显示的提示文本 */
  placeholder?: string;
  /** 格式提示文本 */
  formatHint?: string;
  /** 已选择的文件名 */
  selectedFileName?: string;
  /** 已选择的文件大小（字节） */
  selectedFileSize?: number;
  /** 自定义图标 */
  icon?: ReactNode;
  /** 是否显示移除按钮 */
  showRemoveButton?: boolean;
  /** 移除回调 */
  onRemove?: () => void;
}

interface ImageUploadProps extends BaseUploadProps {
  variant: 'image';
  /** 预览图片URL */
  previewUrl?: string | null;
  /** 占位符文本 */
  placeholder?: string;
  /** 是否显示移除按钮 */
  showRemoveButton?: boolean;
  /** 移除回调 */
  onRemove?: () => void;
}

interface TextureUploadProps extends BaseUploadProps {
  variant: 'texture';
  /** 预览图片URL */
  previewUrl?: string | null;
  /** 占位符文本 */
  placeholder?: string;
  /** 是否显示移除按钮 */
  showRemoveButton?: boolean;
  /** 移除回调 */
  onRemove?: () => void;
}

export type EnhancedUploadProps = FileUploadProps | ImageUploadProps | TextureUploadProps;

export const EnhancedUpload: React.FC<EnhancedUploadProps> = (props) => {
  const {
    onFileSelect,
    validationOptions,
    onError,
    accept,
    disabled = false,
    className = '',
    size = 'medium',
    variant,
    mode = 'inline',
    modalTitle = '上传文件',
    modalVisible = false,
    onModalClose
  } = props;

  const inputRef = useRef<HTMLInputElement | null>(null);
  const [dragOver, setDragOver] = useState(false);

  const handleFileSelectInternal = (file: File) => {
    onFileSelect(file);
    // 在模态框模式下，上传成功后自动关闭模态框
    if (mode === 'modal' && onModalClose) {
      onModalClose();
    }
  };

  const handleErrorInternal = (error: string) => {
    if (onError) {
      onError(error);
    } else {
      console.error('上传错误:', error);
      alert(error);
    }
  };

  const dragHandlers = createDragUploadHandlers(
    handleFileSelectInternal,
    setDragOver,
    validationOptions,
    handleErrorInternal
  );

  const handleClick = () => {
    if (!disabled) {
      inputRef.current?.click();
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileExtension = (fileName: string): string => {
    return fileName.split('.').pop()?.toUpperCase() || '';
  };

  const renderImageContent = (imageProps: ImageUploadProps) => {
    const { previewUrl, placeholder = '点击上传图片', showRemoveButton, onRemove } = imageProps;
    
    return (
      <>
        {previewUrl ? (
          <div className="enhanced-upload__preview-container">
            <img 
              src={previewUrl} 
              alt="预览图" 
              className="enhanced-upload__preview-image" 
            />
            {showRemoveButton && onRemove && (
              <IconButton
                icon={X}
                variant="upload"
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  // 重置input的value，确保删除后可以重新选择相同文件
                  if (inputRef.current) {
                    inputRef.current.value = '';
                  }
                  onRemove();
                }}
              />
            )}
          </div>
        ) : (
          <div className="enhanced-upload__placeholder">
            <Plus className={`enhanced-upload__icon enhanced-upload__icon--${variant}`} />
            <span className="enhanced-upload__placeholder-text">{placeholder}</span>
          </div>
        )}
      </>
    );
  };

  const renderTextureContent = (textureProps: TextureUploadProps) => {
    const { previewUrl, placeholder = '点击或拖拽上传纹理', showRemoveButton, onRemove } = textureProps;
    
    return (
      <>
        {previewUrl ? (
          <div className="enhanced-upload__texture-preview">
            <img src={previewUrl} alt="纹理预览" className="enhanced-upload__texture-image" />
            {showRemoveButton && onRemove && (
              <IconButton
                icon={X}
                variant="upload"
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  // 重置input的value，确保删除后可以重新选择相同文件
                  if (inputRef.current) {
                    inputRef.current.value = '';
                  }
                  onRemove();
                }}
              />
            )}
          </div>
        ) : (
          <div className="enhanced-upload__placeholder">
            <UploadIcon size={24} />
            <span className="enhanced-upload__placeholder-text">{placeholder}</span>
            <small className="enhanced-upload__format-hint">支持 JPG, PNG, WebP 格式</small>
          </div>
        )}
      </>
    );
  };

  const renderFileContent = (fileProps: FileUploadProps) => {
    const {
      placeholder = '点击或拖拽文件到此处上传',
      formatHint,
      selectedFileName,
      selectedFileSize,
      icon,
      showRemoveButton,
      onRemove
    } = fileProps;

    // 如果有选中的文件，显示文件信息状态
    if (selectedFileName) {
      return (
        <div className="enhanced-upload__file-info">
          {icon && <div className="enhanced-upload__file-icon">{icon}</div>}
          <div className="enhanced-upload__file-details">
            <span className="enhanced-upload__file-name">{selectedFileName}</span>
            <div className="enhanced-upload__file-meta">
              <span className="enhanced-upload__file-format">{getFileExtension(selectedFileName)}</span>
              <span className="enhanced-upload__file-separator">•</span>
              <span className="enhanced-upload__file-size">{formatFileSize(selectedFileSize || 0)}</span>
            </div>
          </div>
          {showRemoveButton && onRemove && (
            <IconButton
              icon={X}
              variant="danger"
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                // 重置input的value，确保删除后可以重新选择相同文件
                if (inputRef.current) {
                  inputRef.current.value = '';
                }
                onRemove();
              }}
            />
          )}
        </div>
      );
    }

    // 默认占位符状态
    return (
      <div className="enhanced-upload__placeholder">
        {icon || <UploadIcon size={variant === 'model' ? 48 : 32} />}
        <p className="enhanced-upload__placeholder-text">{placeholder}</p>
        {formatHint && (
          <p className="enhanced-upload__format-hint">{formatHint}</p>
        )}
      </div>
    );
  };

  const renderContent = () => {
    switch (variant) {
      case 'image':
        return renderImageContent(props as ImageUploadProps);
      case 'texture':
        return renderTextureContent(props as TextureUploadProps);
      case 'file':
      case 'model':
      default:
        return renderFileContent(props as FileUploadProps);
    }
  };

  const getUploadAreaClass = () => {
    const baseClass = 'enhanced-upload__area';
    const variantClass = `enhanced-upload__area--${variant}`;
    const sizeClass = `enhanced-upload__area--${size}`;
    const dragClass = dragOver ? 'enhanced-upload__area--drag-over' : '';
    const disabledClass = disabled ? 'enhanced-upload__area--disabled' : '';
    
    return [baseClass, variantClass, sizeClass, dragClass, disabledClass]
      .filter(Boolean)
      .join(' ');
  };

  // 检查是否有已选择的文件（仅对file和model类型）
  const hasSelectedFile = (variant === 'file' || variant === 'model') && 
    (props as FileUploadProps).selectedFileName;

  const uploadComponent = (
    <div className={`enhanced-upload ${className}`}>
      {hasSelectedFile ? (
        // 已上传文件直接展示，不包装在upload area中
        <>
          {renderContent()}
          <input
            ref={inputRef}
            type="file"
            accept={accept}
            className="enhanced-upload__input"
            disabled={disabled}
            onChange={(e) => {
              if (!disabled) {
                handleFileInputChange(
                  e,
                  handleFileSelectInternal,
                  validationOptions,
                  handleErrorInternal
                );
              }
            }}
          />
        </>
      ) : (
        // 未上传状态保持原有的upload area容器
        <>
          <div
            className={getUploadAreaClass()}
            onDrop={disabled ? undefined : dragHandlers.onDrop}
            onDragOver={disabled ? undefined : dragHandlers.onDragOver}
            onDragLeave={disabled ? undefined : dragHandlers.onDragLeave}
            onClick={handleClick}
          >
            {renderContent()}
          </div>

          <input
            ref={inputRef}
            type="file"
            accept={accept}
            className="enhanced-upload__input"
            disabled={disabled}
            onChange={(e) => {
              if (!disabled) {
                handleFileInputChange(
                  e,
                  handleFileSelectInternal,
                  validationOptions,
                  handleErrorInternal
                );
              }
            }}
          />
        </>
      )}
    </div>
  );

  // 如果是模态框模式，包装在 Modal 组件中
  if (mode === 'modal') {
    return (
      <Modal visible={modalVisible} onClose={onModalClose || (() => {})} title={modalTitle}>
        <div className="enhanced-upload__modal-content">
          {uploadComponent}
        </div>
      </Modal>
    );
  }

  // 默认内联模式
  return uploadComponent;
};

// 便捷组件
export const FileUpload: React.FC<Omit<FileUploadProps, 'variant'>> = (props) => (
  <EnhancedUpload {...props} variant="file" />
);

export const ImageUpload: React.FC<Omit<ImageUploadProps, 'variant'>> = (props) => (
  <EnhancedUpload {...props} variant="image" />
);

export const TextureUpload: React.FC<Omit<TextureUploadProps, 'variant'>> = (props) => (
  <EnhancedUpload {...props} variant="texture" />
);

export const ModelUpload: React.FC<Omit<FileUploadProps, 'variant'>> = (props) => (
  <EnhancedUpload {...props} variant="model" />
);

// 模态框模式的便捷组件
export const UploadModal: React.FC<Omit<FileUploadProps, 'variant' | 'mode'> & {
  visible: boolean;
  onClose: () => void;
  title?: string;
  description?: string;
}> = ({ visible, onClose, title, description, ...props }) => (
  <EnhancedUpload
    {...props}
    variant="model"
    mode="modal"
    modalVisible={visible}
    onModalClose={onClose}
    modalTitle={title}
    modalDescription={description}
  />
);