import React, { useState } from 'react';
import './input-box.css';

interface InputBoxProps {
  placeholder?: string;
  value?: string;
  defaultValue?: string;
  onChange?: (value: string) => void;
  onFocus?: () => void;
  onBlur?: () => void;
  onEnter?: (value: string) => void;
  disabled?: boolean;
  /** @deprecated 使用 widthVariant 替代 */
  width?: number;
  fullWidth?: boolean;
  type?: 'text' | 'password' | 'number' | 'email' | 'tel';
  prefixIcon?: React.ReactNode;
  suffixIcon?: React.ReactNode;
  size?: 'small' | 'medium' | 'large';
  /** 宽度变体 */
  widthVariant?: 'narrow' | 'default' | 'wide' | 'full';
}

export const InputBox: React.FC<InputBoxProps> = ({
  placeholder = '',
  value,
  defaultValue = '',
  onChange,
  onFocus,
  onBlur,
  onEnter,
  disabled = false,
  width,
  fullWidth = false,
  type = 'text',
  prefixIcon,
  suffixIcon,
  size = 'medium',
  widthVariant = 'default'
}) => {
  const [inputValue, setInputValue] = useState(value !== undefined ? value : defaultValue);
  const [isFocused, setIsFocused] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    onChange?.(newValue);
  };

  const handleFocus = () => {
    setIsFocused(true);
    onFocus?.();
  };

  const handleBlur = () => {
    setIsFocused(false);
    onBlur?.();
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && onEnter) {
      onEnter(controlledValue);
    }
  };

  const controlledValue = value !== undefined ? value : inputValue;

  // 确定宽度类名
  const getWidthClass = () => {
    if (fullWidth) return 'input-box-container--full';
    if (width) return 'input-box-dynamic-width'; // 使用动态宽度类
    return `input-box-container--${widthVariant}`;
  };

  const containerClasses = [
    'input-box-container',
    isFocused ? 'focused' : '',
    disabled ? 'disabled' : '',
    size,
    getWidthClass()
  ].filter(Boolean).join(' ');

  return (
    <div
      className={containerClasses}
      style={width ? { '--input-width': `${width}px` } as React.CSSProperties : undefined}
    >
      {prefixIcon && (
        <div className="input-prefix-icon">
          {prefixIcon}
        </div>
      )}
      
      <input
        type={type}
        value={controlledValue}
        onChange={handleChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        disabled={disabled}
        className={`input-box ${prefixIcon ? 'has-prefix' : ''} ${suffixIcon ? 'has-suffix' : ''}`}
      />
      
      {suffixIcon && (
        <div className="input-suffix-icon">
          {suffixIcon}
        </div>
      )}
    </div>
  );
};
