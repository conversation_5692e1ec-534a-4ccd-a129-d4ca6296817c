import React from 'react';
import { AlertTriangle, Info, CheckCircle, XCircle } from 'lucide-react';
import type { LucideIcon } from 'lucide-react';
import { Modal } from '../modal/modal';
import { PrimaryButton } from '../primary-button/primary-button';
import { SecondaryButton } from '../secondary-button/secondary-button';
import './confirm-dialog.css';

export interface ConfirmDialogProps {
  /** 是否显示对话框 */
  visible: boolean;
  /** 对话框标题 */
  title: string;
  /** 对话框内容 */
  message: string;
  /** 对话框类型 */
  type?: 'info' | 'warning' | 'error' | 'success';
  /** 自定义图标 */
  icon?: LucideIcon;
  /** 确认按钮文本 */
  confirmText?: string;
  /** 取消按钮文本 */
  cancelText?: string;
  /** 确认按钮变体 */
  confirmVariant?: 'primary' | 'danger';
  /** 是否显示取消按钮 */
  showCancel?: boolean;
  /** 确认回调 */
  onConfirm: () => void;
  /** 取消回调 */
  onCancel: () => void;
  /** 是否正在处理 */
  loading?: boolean;
}

const DEFAULT_ICONS = {
  info: Info,
  warning: AlertTriangle,
  error: XCircle,
  success: CheckCircle
};

const DEFAULT_TEXTS = {
  info: { confirm: '确定', cancel: '取消' },
  warning: { confirm: '继续', cancel: '取消' },
  error: { confirm: '确定', cancel: '取消' },
  success: { confirm: '确定', cancel: '取消' }
};

export const ConfirmDialog: React.FC<ConfirmDialogProps> = ({
  visible,
  title,
  message,
  type = 'info',
  icon,
  confirmText,
  cancelText,
  confirmVariant,
  showCancel = true,
  onConfirm,
  onCancel,
  loading = false
}) => {
  const Icon = icon || DEFAULT_ICONS[type];
  const defaultTexts = DEFAULT_TEXTS[type];
  
  const finalConfirmText = confirmText || defaultTexts.confirm;
  const finalCancelText = cancelText || defaultTexts.cancel;
  const finalConfirmVariant = confirmVariant || (type === 'error' || type === 'warning' ? 'danger' : 'primary');

  const handleConfirm = () => {
    if (!loading) {
      onConfirm();
    }
  };

  const handleCancel = () => {
    if (!loading) {
      onCancel();
    }
  };

  return (
    <Modal
      visible={visible}
      title={title}
      onClose={handleCancel}
      size="small"
      showCloseButton={!loading}
      closeOnOverlayClick={!loading}
    >
      <div className={`confirm-dialog confirm-dialog--${type}`}>
        <div className="confirm-dialog__content">
          <div className="confirm-dialog__icon">
            <Icon className={`confirm-dialog__icon-svg confirm-dialog__icon-svg--${type}`} />
          </div>
          <div className="confirm-dialog__message">
            {message}
          </div>
        </div>
        
        <div className="confirm-dialog__actions">
          {showCancel && (
            <SecondaryButton
              onClick={handleCancel}
              disabled={loading}
            >
              {finalCancelText}
            </SecondaryButton>
          )}
          <PrimaryButton
            onClick={handleConfirm}
            variant={finalConfirmVariant}
            loading={loading}
            disabled={loading}
          >
            {finalConfirmText}
          </PrimaryButton>
        </div>
      </div>
    </Modal>
  );
};
