import React from "react";
import type { LucideIcon } from "lucide-react";
import { Plus } from "lucide-react";
import "./icon-button.css";

interface IconButtonProps {
  /** 图标组件，来自 lucide-react 库 */
  icon?: LucideIcon;
  /** 自定义类名 */
  className?: string;
  /** 按钮禁用状态 */
  disabled?: boolean;
  /** 点击事件处理函数 */
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
  /** 按钮大小，默认为 'medium' */
  size?: 'small' | 'medium' | 'large';
  /** 按钮变体，用于特殊样式如危险操作或上传图片 */
  variant?: 'default' | 'danger' | 'upload';
  /** 按钮类型 */
  type?: 'button' | 'submit' | 'reset';
  /** 子元素 */
  children?: React.ReactNode;
}

export const IconButton: React.FC<IconButtonProps> = ({
  icon: Icon = Plus,
  className = "",
  disabled = false,
  onClick,
  size = "medium",
  variant = "default",
  type = "button",
  children,
}) => {
  const buttonClasses = [
    'icon-button',
    `icon-button--${size}`,
    variant === 'danger' && 'icon-button--danger',
    variant === 'upload' && 'icon-button--upload',
    className,
  ].filter(Boolean).join(' ');

  return (
    <button
      className={buttonClasses}
      disabled={disabled}
      onClick={onClick}
      type={type}
    >
      {Icon && <Icon className="icon-button__icon" />}
      {children}
    </button>
  );
};
