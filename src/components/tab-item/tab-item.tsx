import React from 'react';
import type { LucideIcon } from 'lucide-react';
import './tab-item.css';

export interface TabItemProps {
  /** 标签文本 */
  label: string;
  /** 图标组件 */
  icon?: LucideIcon;
  /** 是否选中 */
  isActive?: boolean;
  /** 点击事件处理函数 */
  onClick?: () => void;
  /** 自定义类名 */
  className?: string;
  /** @deprecated 使用 widthVariant 替代 */
  width?: number | string;
  /** 宽度变体 */
  widthVariant?: 'compact' | 'default' | 'wide';
}

const DEFAULT_TAB_WIDTH = 104; // 对应 --tab-width-default

export const TabItem: React.FC<TabItemProps> = ({
  label,
  icon: Icon,
  isActive = false,
  onClick,
  className = '',
  width = DEFAULT_TAB_WIDTH,
  widthVariant = 'default'
}) => {
  const handleClick = () => {
    if (onClick) {
      onClick();
    }
  };

  // 确定宽度类名
  const getWidthClass = () => {
    if (width !== DEFAULT_TAB_WIDTH) return 'tab-item-dynamic-width'; // 使用动态宽度类
    if (widthVariant === 'default') return 'tab-item--width-default';
    return `tab-item--${widthVariant}`;
  };

  const containerClasses = [
    'tab-item',
    isActive ? 'tab-item--active' : 'tab-item--default',
    getWidthClass(),
    className
  ].filter(Boolean).join(' ');

  // 选中和非选中状态使用不同的文本颜色
  const textColorClass = isActive ? 'tab-item__text--active' : 'tab-item__text--default';
  const iconColorClass = isActive ? 'tab-item__icon--active' : 'tab-item__icon--default';

  return (
    <div
      className={containerClasses}
      onClick={handleClick}
      style={width !== DEFAULT_TAB_WIDTH ? { '--tab-width': typeof width === 'number' ? `${width}px` : width } as React.CSSProperties : undefined}
      data-layer="tab-item"
    >
      {Icon && (
        <div className={`tab-item__icon-container ${iconColorClass}`}>
          <Icon className="tab-item__icon" />
        </div>
      )}
      <div className={`tab-item__text ${textColorClass}`}>{label}</div>
    </div>
  );
};
