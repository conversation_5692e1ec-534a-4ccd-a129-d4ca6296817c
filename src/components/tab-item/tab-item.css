/* 标签项基础样式 */
.tab-item {
  height: var(--button-height-small);
  padding: var(--spacing-xs);
  display: inline-flex;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-xs);
  border-radius: var(--radius-s);
  cursor: pointer;
  user-select: none;
  box-sizing: border-box;
  transition: var(--transition-base);
}

/* 默认状态 */
.tab-item--default {
  background: transparent;
}

/* 悬停状态 */
.tab-item--default:hover {
  background: var(--color-bg-hover);
}

/* 激活状态 */
.tab-item--active {
  background: var(--color-support);
}

/* 文本样式 */
.tab-item__text {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  word-wrap: break-word;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 默认状态文本颜色 */
.tab-item__text--default {
  color: var(--color-content-regular, rgba(255, 255, 255, 0.70));
}

/* 激活状态文本颜色 */
.tab-item__text--active {
  color: var(--color-content-accent);
}

/* 图标容器 */
.tab-item__icon-container {
  width: var(--icon-size-medium);
  height: var(--icon-size-medium);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 图标样式 */
.tab-item__icon {
  width: var(--icon-size-medium);
  height: var(--icon-size-medium);
}

/* 默认状态图标颜色 */
.tab-item__icon--default svg {
  stroke: var(--color-content-regular);
  stroke-width: var(--border-width);
}

/* 激活状态图标颜色 */
.tab-item__icon--active svg {
  stroke: var(--color-content-accent);
  stroke-width: var(--border-width);
}

/* 适配浅色主题 */
.theme-light .tab-item--active {
  background: var(--color-support);
}

/* 宽度变体 */
.tab-item--compact {
  width: var(--tab-width-compact);
}

.tab-item--width-default {
  width: var(--tab-width-default);
}

.tab-item--wide {
  width: var(--tab-width-wide);
}