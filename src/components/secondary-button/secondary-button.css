.secondary-button {
  /* 继承统一的按钮基础样式 */
  background: var(--color-support);
  color: var(--color-content-accent);
}

.secondary-button:hover:not(:disabled) {
  background: var(--color-bg-hover);
}

/* 次要按钮特有的样式变体 - 基础样式已在components.css中定义 */

/* 变体样式 */
.secondary-button--default {
  /* 默认样式已在基础类中定义 */
}

.secondary-button--danger {
  color: var(--color-error);
  border: 1px solid var(--color-error);
  background: transparent;
}

.secondary-button--danger:hover:not(:disabled) {
  background-color: var(--color-error);
  color: var(--color-content-invert);
}