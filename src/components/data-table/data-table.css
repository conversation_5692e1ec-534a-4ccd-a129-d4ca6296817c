/* 数据表格样式 */
.data-table-container {
  width: 100%;
  overflow-x: auto;
  border-radius: var(--radius-base);
  border: var(--border-width) solid var(--color-border);
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  background-color: var(--color-bg-primary);
  font-size: var(--font-size-sm);
}

/* 表格尺寸变体 */
.data-table--small {
  font-size: var(--font-size-xs);
}

.data-table--medium {
  font-size: var(--font-size-sm);
}

.data-table--large {
  font-size: var(--font-size-base);
}

/* 边框变体 */
.data-table--bordered .data-table__header-cell,
.data-table--bordered .data-table__cell {
  border-right: var(--border-width) solid var(--color-border);
}

.data-table--bordered .data-table__header-cell:last-child,
.data-table--bordered .data-table__cell:last-child {
  border-right: none;
}

/* 斑马纹变体 */
.data-table--striped .data-table__row:nth-child(even) {
  background-color: var(--color-bg-secondary);
}

/* 表头样式 */
.data-table__header {
  background-color: var(--color-bg-secondary);
}

.data-table__header-cell {
  padding: var(--spacing-sm) var(--spacing-md);
  text-align: left;
  font-weight: var(--font-weight-medium);
  color: var(--color-content-primary);
  border-bottom: var(--border-width) solid var(--color-border);
  position: relative;
  user-select: none;
}

.data-table__header-cell--center {
  text-align: center;
}

.data-table__header-cell--right {
  text-align: right;
}

.data-table__header-cell--sortable {
  cursor: pointer;
  transition: background-color var(--transition-base);
}

.data-table__header-cell--sortable:hover {
  background-color: var(--color-bg-hover);
}

.data-table__header-cell--sorted {
  background-color: var(--color-bg-hover);
}

.data-table__header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-xs);
}

.data-table__sort-icons {
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.data-table__sort-icon {
  width: var(--icon-size-small);
  height: var(--icon-size-small);
  color: var(--color-content-mute);
  transition: color var(--transition-base);
}

.data-table__sort-icon--active {
  color: var(--color-brand);
}

/* 表体样式 */
.data-table__body {
  background-color: var(--color-bg-primary);
}

.data-table__row {
  transition: background-color var(--transition-base);
}

.data-table__row--clickable {
  cursor: pointer;
}

.data-table__row--clickable:hover {
  background-color: var(--color-bg-hover);
}

.data-table__cell {
  padding: var(--spacing-sm) var(--spacing-md);
  border-bottom: var(--border-width) solid var(--color-border);
  color: var(--color-content-primary);
  vertical-align: middle;
}

.data-table__cell--center {
  text-align: center;
}

.data-table__cell--right {
  text-align: right;
}

/* 尺寸变体的内边距 */
.data-table--small .data-table__header-cell,
.data-table--small .data-table__cell {
  padding: var(--spacing-xs) var(--spacing-sm);
}

.data-table--large .data-table__header-cell,
.data-table--large .data-table__cell {
  padding: var(--spacing-md) var(--spacing-lg);
}

/* 空状态和加载状态 */
.data-table .loading,
.data-table .empty-state {
  padding: var(--spacing-xl);
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .data-table-container {
    font-size: var(--font-size-xs);
  }
  
  .data-table__header-cell,
  .data-table__cell {
    padding: var(--spacing-xs) var(--spacing-sm);
  }
  
  .data-table__header-content {
    flex-direction: column;
    gap: var(--spacing-xxs);
  }
}
