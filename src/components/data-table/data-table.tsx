import React from 'react';
import { ChevronUp, ChevronDown } from 'lucide-react';
import { Loading } from '../loading/loading';
import { EmptyState } from '../empty-state/empty-state';
import './data-table.css';

export interface TableColumn<T = Record<string, unknown>> {
  /** 列的唯一标识 */
  key: string;
  /** 列标题 */
  title: string;
  /** 数据字段名 */
  dataIndex?: keyof T;
  /** 自定义渲染函数 */
  render?: (value: unknown, record: T, index: number) => React.ReactNode;
  /** 列宽度 */
  width?: string | number;
  /** 是否可排序 */
  sortable?: boolean;
  /** 对齐方式 */
  align?: 'left' | 'center' | 'right';
  /** 是否固定列 */
  fixed?: 'left' | 'right';
}

export interface SortConfig {
  key: string;
  direction: 'asc' | 'desc';
}

export interface DataTableProps<T = Record<string, unknown>> {
  /** 表格列配置 */
  columns: TableColumn<T>[];
  /** 表格数据 */
  data: T[];
  /** 行的唯一标识字段 */
  rowKey?: keyof T | ((record: T) => string);
  /** 是否显示加载状态 */
  loading?: boolean;
  /** 空状态配置 */
  emptyState?: {
    message?: string;
    description?: string;
  };
  /** 排序配置 */
  sortConfig?: SortConfig;
  /** 排序变化回调 */
  onSortChange?: (sortConfig: SortConfig | null) => void;
  /** 行点击事件 */
  onRowClick?: (record: T, index: number) => void;
  /** 自定义类名 */
  className?: string;
  /** 表格尺寸 */
  size?: 'small' | 'medium' | 'large';
  /** 是否显示边框 */
  bordered?: boolean;
  /** 是否显示斑马纹 */
  striped?: boolean;
}

export const DataTable = <T extends Record<string, unknown>>({
  columns,
  data,
  rowKey = 'id',
  loading = false,
  emptyState,
  sortConfig,
  onSortChange,
  onRowClick,
  className = '',
  size = 'medium',
  bordered = true,
  striped = true
}: DataTableProps<T>) => {
  const getRowKey = (record: T, index: number): string => {
    if (typeof rowKey === 'function') {
      return rowKey(record);
    }
    return String(record[rowKey] || index);
  };

  const handleSort = (column: TableColumn<T>) => {
    if (!column.sortable || !onSortChange) return;

    const currentKey = sortConfig?.key;
    const currentDirection = sortConfig?.direction;

    let newSortConfig: SortConfig | null = null;

    if (currentKey === column.key) {
      // 同一列：升序 -> 降序 -> 无排序
      if (currentDirection === 'asc') {
        newSortConfig = { key: column.key, direction: 'desc' };
      } else if (currentDirection === 'desc') {
        newSortConfig = null;
      }
    } else {
      // 不同列：默认升序
      newSortConfig = { key: column.key, direction: 'asc' };
    }

    onSortChange(newSortConfig);
  };

  const renderCell = (column: TableColumn<T>, record: T, index: number) => {
    if (column.render) {
      return column.render(record[column.dataIndex!], record, index);
    }
    return record[column.dataIndex!];
  };

  const tableClasses = [
    'data-table',
    `data-table--${size}`,
    bordered ? 'data-table--bordered' : '',
    striped ? 'data-table--striped' : '',
    className
  ].filter(Boolean).join(' ');

  if (loading) {
    return (
      <div className="data-table-container">
        <div className={tableClasses}>
          <Loading text="加载中..." />
        </div>
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <div className="data-table-container">
        <div className={tableClasses}>
          <EmptyState
            message={emptyState?.message || '暂无数据'}
            description={emptyState?.description}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="data-table-container">
      <table className={tableClasses}>
        <thead className="data-table__header">
          <tr>
            {columns.map((column) => {
              const isCurrentSort = sortConfig?.key === column.key;
              const sortDirection = isCurrentSort ? sortConfig.direction : null;
              
              const headerClasses = [
                'data-table__header-cell',
                column.align ? `data-table__header-cell--${column.align}` : '',
                column.sortable ? 'data-table__header-cell--sortable' : '',
                isCurrentSort ? 'data-table__header-cell--sorted' : ''
              ].filter(Boolean).join(' ');

              return (
                <th
                  key={column.key}
                  className={headerClasses}
                  style={{ width: column.width }}
                  onClick={() => handleSort(column)}
                >
                  <div className="data-table__header-content">
                    <span>{column.title}</span>
                    {column.sortable && (
                      <div className="data-table__sort-icons">
                        <ChevronUp 
                          className={`data-table__sort-icon ${
                            sortDirection === 'asc' ? 'data-table__sort-icon--active' : ''
                          }`}
                        />
                        <ChevronDown 
                          className={`data-table__sort-icon ${
                            sortDirection === 'desc' ? 'data-table__sort-icon--active' : ''
                          }`}
                        />
                      </div>
                    )}
                  </div>
                </th>
              );
            })}
          </tr>
        </thead>
        <tbody className="data-table__body">
          {data.map((record, index) => {
            const rowClasses = [
              'data-table__row',
              onRowClick ? 'data-table__row--clickable' : ''
            ].filter(Boolean).join(' ');

            return (
              <tr
                key={getRowKey(record, index)}
                className={rowClasses}
                onClick={() => onRowClick?.(record, index)}
              >
                {columns.map((column) => {
                  const cellClasses = [
                    'data-table__cell',
                    column.align ? `data-table__cell--${column.align}` : ''
                  ].filter(Boolean).join(' ');

                  return (
                    <td
                      key={column.key}
                      className={cellClasses}
                      style={{ width: column.width }}
                    >
                      {renderCell(column, record, index)}
                    </td>
                  );
                })}
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );
};
