import React, { useRef, useMemo, useEffect } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { Stage } from '@react-three/drei';
import * as THREE from 'three';
import { loadTexture } from '../../utils/threeUtils';
import { registerWebGLContext, safeReleaseContext } from '../../utils/webglContextManager';
import type { MaterialSettings } from '../custom-material-panel/custom-material-panel';
import './material-preview.css';

interface MaterialPreviewProps {
  settings: MaterialSettings;
  /**
   * Canvas 容器尺寸（宽高一致，默认 120px）。
   * 传入 0 或 undefined 时使用父元素 100% 宽高
   */
  size?: number;
}

/**
 * 后台材质编辑弹窗中的实时材质预览。
 * 使用与前台渲染页完全一致的环境贴图和灯光配置，确保所见即所得。
 * 优化WebGL上下文使用，防止上下文丢失。
 */
const MaterialPreview: React.FC<MaterialPreviewProps> = ({ settings, size = 120 }) => {
  const sphereRef = useRef<THREE.Mesh>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);

  // 清理 WebGLRenderer，防止后台预览反复打开导致上下文泄漏
  useEffect(() => {
    return () => {
      if (rendererRef.current) {
        try {
          safeReleaseContext(rendererRef.current);
          rendererRef.current.dispose();
        } catch (error) {
          console.warn('Error disposing material preview WebGL context:', error);
        } finally {
          rendererRef.current = null;
        }
      }
    };
  }, []);

  // 使用useMemo缓存纹理，避免每次渲染都重新加载
  const texture = useMemo(() => {
    if (!settings.textureUrl) return null;
    return loadTexture(settings.textureUrl);
  }, [settings.textureUrl]);

  /**
   * 旋转动画，让材质球缓慢转动，展示高光和金属度效果。
   */
  const RotatingSphere: React.FC = () => {
    useFrame(() => {
      if (sphereRef.current) {
        sphereRef.current.rotation.y += 0.01;
      }
    });

    return (
      <mesh ref={sphereRef}>
        <sphereGeometry args={[1, 64, 64]} />
        <meshStandardMaterial
          color={new THREE.Color(settings.color)}
          metalness={settings.metalness}
          roughness={settings.roughness}
          transparent={settings.opacity < 1}
          opacity={settings.opacity}
          map={texture}
        />
      </mesh>
    );
  };

  return (
    <div
      className="material-preview"
      style={{
        width: size || '100%',
        height: size || '100%',
      }}
    >
      <Canvas
        frameloop="demand"
        onCreated={({ gl, invalidate }) => {
          // 注册WebGL上下文
          registerWebGLContext(gl);
          
          rendererRef.current = gl;
          // 设置透明背景
          gl.setClearColor(0x000000, 0);
          
          // 添加上下文丢失监听
          const canvas = gl.domElement;
          const handleContextLost = (event: Event) => {
            event.preventDefault();
            console.warn('Material preview WebGL context lost');
          };
          const handleContextRestored = () => {
            console.log('Material preview WebGL context restored');
            invalidate();
          };
          
          canvas.addEventListener('webglcontextlost', handleContextLost);
          canvas.addEventListener('webglcontextrestored', handleContextRestored);
          
          return () => {
            canvas.removeEventListener('webglcontextlost', handleContextLost);
            canvas.removeEventListener('webglcontextrestored', handleContextRestored);
          };
        }}
        gl={{
          antialias: true,
          alpha: true,
          powerPreference: 'default',
          preserveDrawingBuffer: false,
          stencil: false
        }}
        className="material-preview-canvas canvas-circle"
        camera={{ position: [0, 0, 2.5], fov: 50, near: 0.1, far: 1000 }}
      >
        {/* 与 RenderPage 完全一致的 Stage 配置 */}
        <Stage
          environment="city"
          intensity={0.6}
          adjustCamera={false}
          shadows={false}
          preset="rembrandt"
          scale={1}
        >
          <group position={[0, 0, 0]}>
            <RotatingSphere />
          </group>
        </Stage>
      </Canvas>
    </div>
  );
};

export default MaterialPreview;