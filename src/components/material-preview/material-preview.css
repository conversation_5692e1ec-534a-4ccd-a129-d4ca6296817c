/* 材质预览组件样式 */
.material-preview {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  overflow: hidden;
  position: relative;
  padding: 15px;
  box-sizing: border-box;
  /* 移除背景色，保持透明 */
}

.material-preview canvas {
  display: block;
  width: 100%;
  height: 100%;
  margin: 0 auto;
}

/* 加载状态 - 使用统一的Loading组件，移除重复的动画定义 */
.material-preview--loading {
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-bg-overlay);
}
