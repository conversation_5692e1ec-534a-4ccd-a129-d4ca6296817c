import React from 'react';
import { Toolbar } from '../toolbar/toolbar';
import './page-layout.css';

export interface PageHeaderProps {
  /** 页面标题 */
  title: string;
  /** 页面描述 */
  description?: string;
  /** 左侧操作区域（通常是搜索框） */
  leftActions?: React.ReactNode;
  /** 右侧操作区域（通常是按钮） */
  rightActions?: React.ReactNode;
  /** 自定义类名 */
  className?: string;
}

export const PageHeader: React.FC<PageHeaderProps> = ({
  title,
  description,
  leftActions,
  rightActions,
  className = ''
}) => {
  const headerClasses = [
    'page-header',
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={headerClasses}>
      <div className="page-header__content">
        <div className="page-header__info">
          <h1 className="page-header__title">{title}</h1>
          {description && (
            <p className="page-header__description">{description}</p>
          )}
        </div>
      </div>
      
      {(leftActions || rightActions) && (
        <Toolbar
          left={leftActions}
          right={rightActions}
          className="page-header__toolbar"
        />
      )}
    </div>
  );
};

export interface PageContentProps {
  /** 页面内容 */
  children: React.ReactNode;
  /** 自定义类名 */
  className?: string;
  /** 内容区域内边距 */
  padding?: 'none' | 'small' | 'medium' | 'large';
}

export const PageContent: React.FC<PageContentProps> = ({
  children,
  className = '',
  padding = 'medium'
}) => {
  const contentClasses = [
    'page-content',
    `page-content--${padding}`,
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={contentClasses}>
      {children}
    </div>
  );
};

export interface PageLayoutProps {
  /** 页面标题 */
  title: string;
  /** 页面描述 */
  description?: string;
  /** 左侧操作区域 */
  leftActions?: React.ReactNode;
  /** 右侧操作区域 */
  rightActions?: React.ReactNode;
  /** 页面内容 */
  children: React.ReactNode;
  /** 自定义类名 */
  className?: string;
  /** 内容区域内边距 */
  contentPadding?: 'none' | 'small' | 'medium' | 'large';
  /** 是否显示分隔线 */
  showDivider?: boolean;
}

export const PageLayout: React.FC<PageLayoutProps> = ({
  title,
  description,
  leftActions,
  rightActions,
  children,
  className = '',
  contentPadding = 'medium',
  showDivider = true
}) => {
  const layoutClasses = [
    'page-layout',
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={layoutClasses}>
      <PageHeader
        title={title}
        description={description}
        leftActions={leftActions}
        rightActions={rightActions}
        className={showDivider ? 'page-header--with-divider' : ''}
      />
      <PageContent padding={contentPadding}>
        {children}
      </PageContent>
    </div>
  );
};
