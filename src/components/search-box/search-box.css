/* 搜索框组件样式 - 独立样式系统 */
.search-box {
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--color-bg-overlay);
  border-radius: var(--radius-base);
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: var(--spacing-sm);
  height: var(--button-height);
  width: 100%;
  box-sizing: border-box;
  cursor: text;
  transition: background-color 0.2s ease;
}

.search-box:hover:not(.search-box--disabled) {
  background: var(--color-bg-hover);
}

.search-box__icon-container {
  width: var(--icon-size-medium);
  height: var(--icon-size-medium);
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  border-radius: var(--radius-xs);
  transition: var(--transition-base);
}

.search-box__icon-container:hover:not(.search-box--disabled .search-box__icon-container) {
  background: var(--color-bg-hover);
}

.search-box__icon {
  width: var(--icon-size-medium);
  height: var(--icon-size-medium);
  color: var(--color-content-mute);
  stroke-width: var(--border-width);
  transition: var(--transition-base);
}

.search-box__icon-container:hover .search-box__icon:not(.search-box--disabled .search-box__icon) {
  color: var(--color-content-accent);
}

.search-box__input {
  flex-grow: 1;
  border: none;
  background: transparent;
  outline: none;
  padding: 0;
  color: var(--color-content-accent);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
}

.search-box__input::placeholder {
  color: var(--color-content-mute);
}

/* 禁用状态 */
.search-box--disabled {
  opacity: var(--opacity-muted);
  cursor: not-allowed;
}

.search-box--disabled .search-box__input {
  cursor: not-allowed;
  pointer-events: none;
}

.search-box--disabled .search-box__icon-container {
  cursor: not-allowed;
}

/* 管理页面搜索框样式 */
.search-box--management {
  width: var(--input-width-search);
}

/* 移除未使用的浅色主题样式 */