.slider {
  position: relative;
  height: 36px;
  width: 100%;
  max-width: 100%;
  background-color: var(--color-bg-overlay);
  border-radius: var(--radius-base);
  cursor: pointer;
  user-select: none;
  box-sizing: border-box;
  overflow: hidden;
  display: block;
  transition: border-color 0.2s ease;
}

.slider__fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background-color: var(--color-content-secondary);
  border-radius: inherit;
  transition: width 0.2s ease;
  z-index: 1;
}

.slider--dragging .slider__fill {
  transition: none;
}

.slider--disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.slider--disabled:hover {
  border-color: var(--color-border);
}
