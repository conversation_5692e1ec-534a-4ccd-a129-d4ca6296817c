import React from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { IconButton } from '../icon-button/icon-button';
import './pagination.css';

export interface PaginationProps {
  /** 当前页码 */
  current: number;
  /** 总条目数 */
  total: number;
  /** 每页条目数 */
  pageSize: number;
  /** 页码变化回调 */
  onChange: (page: number) => void;
  /** 每页条目数变化回调 */
  onPageSizeChange?: (pageSize: number) => void;
  /** 是否显示每页条目数选择器 */
  showSizeChanger?: boolean;
  /** 每页条目数选项 */
  pageSizeOptions?: number[];
  /** 是否显示快速跳转 */
  showQuickJumper?: boolean;
  /** 是否显示总数 */
  showTotal?: boolean;
  /** 自定义总数显示 */
  totalText?: (total: number, range: [number, number]) => string;
  /** 是否禁用 */
  disabled?: boolean;
  /** 额外的 CSS 类名 */
  className?: string;
}

export const Pagination: React.FC<PaginationProps> = ({
  current,
  total,
  pageSize,
  onChange,
  onPageSizeChange,
  showSizeChanger = true,
  pageSizeOptions = [10, 20, 50, 100],
  showTotal = true,
  totalText,
  disabled = false,
  className = ''
}) => {
  const totalPages = Math.ceil(total / pageSize);
  const startIndex = (current - 1) * pageSize + 1;
  const endIndex = Math.min(current * pageSize, total);

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages && page !== current && !disabled) {
      onChange(page);
    }
  };

  const handlePageSizeChange = (newPageSize: number) => {
    if (onPageSizeChange && newPageSize !== pageSize) {
      onPageSizeChange(newPageSize);
      // 调整当前页码，确保不超出范围
      const newTotalPages = Math.ceil(total / newPageSize);
      if (current > newTotalPages) {
        onChange(Math.max(1, newTotalPages));
      }
    }
  };

  const renderPageNumbers = () => {
    const pages: React.ReactNode[] = [];
    const maxVisiblePages = 7;
    
    if (totalPages <= maxVisiblePages) {
      // 显示所有页码
      for (let i = 1; i <= totalPages; i++) {
        pages.push(
          <button
            key={i}
            className={`pagination__page ${i === current ? 'pagination__page--active' : ''}`}
            onClick={() => handlePageChange(i)}
            disabled={disabled}
          >
            {i}
          </button>
        );
      }
    } else {
      // 显示部分页码
      const startPage = Math.max(1, current - 2);
      const endPage = Math.min(totalPages, current + 2);

      if (startPage > 1) {
        pages.push(
          <button
            key={1}
            className="pagination__page"
            onClick={() => handlePageChange(1)}
            disabled={disabled}
          >
            1
          </button>
        );
        if (startPage > 2) {
          pages.push(
            <span key="start-ellipsis" className="pagination__ellipsis">
              ...
            </span>
          );
        }
      }

      for (let i = startPage; i <= endPage; i++) {
        pages.push(
          <button
            key={i}
            className={`pagination__page ${i === current ? 'pagination__page--active' : ''}`}
            onClick={() => handlePageChange(i)}
            disabled={disabled}
          >
            {i}
          </button>
        );
      }

      if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
          pages.push(
            <span key="end-ellipsis" className="pagination__ellipsis">
              ...
            </span>
          );
        }
        pages.push(
          <button
            key={totalPages}
            className="pagination__page"
            onClick={() => handlePageChange(totalPages)}
            disabled={disabled}
          >
            {totalPages}
          </button>
        );
      }
    }

    return pages;
  };

  const defaultTotalText = (total: number, range: [number, number]) => {
    return `显示 ${range[0]}-${range[1]} 条，共 ${total} 条`;
  };

  if (total === 0) {
    return null;
  }

  return (
    <div className={`pagination ${className}`}>
      {showTotal && (
        <div className="pagination__total">
          {totalText ? totalText(total, [startIndex, endIndex]) : defaultTotalText(total, [startIndex, endIndex])}
        </div>
      )}
      
      <div className="pagination__controls">
        <IconButton
          icon={ChevronLeft}
          size="small"
          onClick={() => handlePageChange(current - 1)}
          disabled={disabled || current <= 1}
          className="pagination__prev"
        />
        
        <div className="pagination__pages">
          {renderPageNumbers()}
        </div>
        
        <IconButton
          icon={ChevronRight}
          size="small"
          onClick={() => handlePageChange(current + 1)}
          disabled={disabled || current >= totalPages}
          className="pagination__next"
        />
      </div>
      
      {showSizeChanger && onPageSizeChange && (
        <div className="pagination__size-changer">
          <span className="pagination__size-label">每页</span>
          <select
            value={pageSize}
            onChange={(e) => handlePageSizeChange(Number(e.target.value))}
            disabled={disabled}
            className="pagination__size-select"
          >
            {pageSizeOptions.map(size => (
              <option key={size} value={size}>
                {size}
              </option>
            ))}
          </select>
          <span className="pagination__size-label">条</span>
        </div>
      )}
    </div>
  );
};

export default Pagination;