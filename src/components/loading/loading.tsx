import React from 'react';
import './loading.css';

export interface LoadingProps {
  /** 加载文本，默认为 "加载中..." */
  text?: string;
  /** 是否显示文本，默认为 true */
  showText?: boolean;
  /** 自定义类名 */
  className?: string;
  /** 是否居中显示，默认为 true */
  centered?: boolean;
  /** 加载组件变体 */
  variant?: 'default' | 'minimal' | 'overlay';
  /** 尺寸 */
  size?: 'small' | 'medium' | 'large';
}

export const Loading: React.FC<LoadingProps> = ({
  text = '加载中...',
  showText = true,
  className = '',
  centered = true,
  variant = 'default',
  size = 'medium'
}) => {
  const containerClasses = [
    'loading',
    `loading--${variant}`,
    `loading--${size}`,
    centered ? 'loading--centered' : '',
    className,
  ].filter(Boolean).join(' ');

  return (
    <div className={containerClasses}>
      <div className="loading__spinner" />
      {showText && <div className="loading__text">{text}</div>}
    </div>
  );
};

export default Loading;
