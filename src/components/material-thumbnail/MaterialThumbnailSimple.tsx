import React, { memo, useRef, useEffect } from 'react';
import { Canvas } from '@react-three/fiber';
import { Environment } from '@react-three/drei';
import * as THREE from 'three';
import { registerWebGLContext, safeReleaseContext } from '../../utils/webglContextManager';
import type { MaterialData } from '../../services/api';

interface MaterialThumbnailSimpleProps {
  material: MaterialData;
  size?: number;
}

// 静态球体组件
const StaticSphere: React.FC<{ material: MaterialData }> = ({ material }) => {
  return (
    <mesh>
      <sphereGeometry args={[1, 48, 48]} />
      <meshStandardMaterial
        color={new THREE.Color(material.data.color)}
        metalness={Math.max(0, Math.min(1, material.data.metalness || 0))}
        roughness={Math.max(0, Math.min(1, material.data.roughness || 0))}
        transparent={(material.data.glass || 0) > 0}
        opacity={(material.data.glass || 0) > 0 ? Math.max(0, Math.min(1, 1 - (material.data.glass || 0))) : 1}
      />
    </mesh>
  );
};

/**
 * 简化的材质缩略图组件，专为表格中的缩略图设计
 */
const MaterialThumbnailSimple: React.FC<MaterialThumbnailSimpleProps> = ({ 
  material, 
  size = 40 
}) => {
  const canvasRef = useRef<HTMLDivElement>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);

  // 组件卸载时主动释放 WebGL 上下文，避免 "Context Lost"
  useEffect(() => {
    return () => {
      if (rendererRef.current) {
        safeReleaseContext(rendererRef.current);
        rendererRef.current = null;
      }
    };
  }, []);
  
  return (
    <div 
      ref={canvasRef}
      className="simple-thumbnail-container"
      style={{
        '--thumbnail-size': `${size}px`
      } as React.CSSProperties}
    >
      <Canvas
        frameloop="demand"
        onCreated={({ gl, invalidate }) => {
          // 注册WebGL上下文（静默模式，不显示通知）
          try {
            registerWebGLContext(gl);
          } catch (error) {
            console.warn('Failed to register WebGL context for thumbnail:', error);
          }
           
           rendererRef.current = gl;
           
           // 添加上下文丢失监听
           const canvas = gl.domElement;
           const handleContextLost = (event: Event) => {
             event.preventDefault();
             console.warn('Simple thumbnail WebGL context lost');
           };
           const handleContextRestored = () => {
             console.log('Simple thumbnail WebGL context restored');
             invalidate();
           };
           
           canvas.addEventListener('webglcontextlost', handleContextLost);
           canvas.addEventListener('webglcontextrestored', handleContextRestored);
           
           return () => {
             canvas.removeEventListener('webglcontextlost', handleContextLost);
             canvas.removeEventListener('webglcontextrestored', handleContextRestored);
           };
         }}
        camera={{ position: [0, 0, 1.5], fov: 45 }}
        gl={{ 
          antialias: true, 
          alpha: true, 
          powerPreference: 'default',
          preserveDrawingBuffer: false,
          stencil: false
        }}
        className="simple-thumbnail-canvas"
      >
        <ambientLight intensity={0.7} />
        <directionalLight position={[5, 5, 5]} intensity={1} />
        <StaticSphere material={material} />
        <Environment preset="city" />
      </Canvas>
    </div>
  );
};

export default memo(MaterialThumbnailSimple);
