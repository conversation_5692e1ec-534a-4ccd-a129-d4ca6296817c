import React, { memo, useRef, useEffect, useState } from 'react';
import { Canvas } from '@react-three/fiber';
import { Environment, useTexture } from '@react-three/drei';
import * as THREE from 'three';
import type { MaterialData } from '../../services/api';
import { registerWebGLContext, canCreateNewContext, safeReleaseContext } from '../../utils/webglContextManager';
import './material-thumbnail.css';

interface MaterialThumbnailProps {
  material: MaterialData;
  active: boolean;
  onClick?: () => void;
  size?: 'default' | 'large' | 'preset';
  textureUrl?: string;
}

// A 1x1 transparent pixel. This is a valid image source used as a placeholder
// to ensure useTexture is always called with a valid URL, complying with Rules of Hooks.
const PLACEHOLDER_IMG = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7';

// This inner component is necessary to use R3F hooks like useTexture.
const Scene = ({ material, textureUrl }: Pick<MaterialThumbnailProps, 'material' | 'textureUrl'>) => {
  // useTexture must be called unconditionally. We provide a placeholder if textureUrl is absent.
  const map = useTexture(textureUrl || PLACEHOLDER_IMG);

  // Don't render if material data is not yet available to prevent flash of default content.
  if (!material) return null;

  // Safely destructure with default values to prevent crashes if data is missing.
  const { color = '#ffffff', metalness = 0, roughness = 50, glass = 0 } = material.data || {};
  const displayColor = (color === '#000000' && !textureUrl) ? '#ffffff' : color;

  // 优化材质参数转换
  const materialProps = {
    color: displayColor,
    metalness: Math.max(0, Math.min(1, metalness)),
    roughness: Math.max(0, Math.min(1, roughness)),
    transparent: glass > 0,
    opacity: glass > 0 ? Math.max(0, Math.min(1, 1 - glass)) : 1,
    map: textureUrl ? map : null
  };

  return (
    <>
      <ambientLight intensity={0.7} />
      <directionalLight position={[5, 5, 5]} intensity={1} />
      <group position={[0, 0, 0]}>
        <mesh position={[0, 0, 0]}>
          <sphereGeometry args={[1, 48, 48]} />
          <meshStandardMaterial {...materialProps} />
        </mesh>
      </group>
      <Environment preset="city" />
    </>
  );
};

// 使用全局WebGL上下文管理器

/**
 * Renders a 3D sphere thumbnail for a material.
 * - Uses frameloop="demand" for performance.
 * - Implements context pooling to prevent WebGL context loss.
 * - Limits the number of active WebGL contexts.
 */
const MaterialThumbnail: React.FC<MaterialThumbnailProps> = ({ material, active, onClick, size = 'default', textureUrl }) => {
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const invalidateRef = useRef<(() => void) | null>(null);
  const [canRender, setCanRender] = useState(true);
  const containerRef = useRef<HTMLDivElement>(null);

  // 检查是否可以创建新的WebGL上下文
  useEffect(() => {
    if (!canCreateNewContext()) {
      setCanRender(false);
      return;
    }
    
    setCanRender(true);
  }, []);

  // Effect to invalidate the canvas when material properties change
  useEffect(() => {
    if (invalidateRef.current && canRender) {
      invalidateRef.current();
    }
  }, [material, textureUrl, canRender]);

  // 组件卸载时主动释放 WebGL 上下文，避免 "Context Lost"
  useEffect(() => {
    return () => {
      if (rendererRef.current) {
        try {
          // safeReleaseContext 已经包含了 dispose 调用，无需重复
          safeReleaseContext(rendererRef.current);
        } catch (error) {
          console.warn('Error disposing material thumbnail WebGL context:', error);
        } finally {
          rendererRef.current = null;
        }
      }
    };
  }, []);

  // 如果无法渲染，显示静态预览
  if (!canRender) {
    const { color = '#ffffff' } = material.data || {};
    return (
      <div
        ref={containerRef}
        className={`material-item${active ? ' active' : ''} ${size === 'large' ? 'material-item-large' : ''} ${size === 'preset' ? 'material-item-preset' : ''}`}
        onClick={onClick}
      >
        <div className="thumbnail-canvas">
          <div 
            className="static-material-preview"
            style={{
              background: `linear-gradient(135deg, ${color}, ${color}dd)`
            }}
          />
        </div>
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className={`material-item${active ? ' active' : ''} ${size === 'large' ? 'material-item-large' : ''} ${size === 'preset' ? 'material-item-preset' : ''}`}
      onClick={onClick}
    >
      <div className="thumbnail-canvas">
        <Canvas
          frameloop="demand"
          camera={{ position: [0, 0, 2.5], fov: 50, near: 0.1, far: 1000 }}
          onCreated={({ gl, invalidate }) => {
          // 尝试注册WebGL上下文
          if (!registerWebGLContext(gl)) {
            console.warn('Failed to register WebGL context for material thumbnail');
            setCanRender(false);
            return;
          }
          
          rendererRef.current = gl;
          invalidateRef.current = invalidate;
          
          // 添加WebGL上下文丢失和恢复监听
          const canvas = gl.domElement;
          const handleContextLost = (event: Event) => {
            event.preventDefault();
            // 静默处理上下文丢失，避免重复日志
            setCanRender(false);
          };
          const handleContextRestored = () => {
            console.log('Material thumbnail WebGL context restored');
            setCanRender(true);
            if (invalidateRef.current) {
              invalidateRef.current();
            }
          };
          
          canvas.addEventListener('webglcontextlost', handleContextLost);
          canvas.addEventListener('webglcontextrestored', handleContextRestored);
          
          return () => {
            canvas.removeEventListener('webglcontextlost', handleContextLost);
            canvas.removeEventListener('webglcontextrestored', handleContextRestored);
          };
        }}
          gl={{ 
            antialias: true, // 启用抗锯齿提高渲染质量
            alpha: true, 
            powerPreference: 'default', // 使用默认性能模式
            preserveDrawingBuffer: false,
            stencil: false
          }}
        >
          <Scene material={material} textureUrl={textureUrl} />
        </Canvas>
      </div>
    </div>
  );
};

// 使用 React.memo，但移除自定义比较函数，以便材质属性变化时能正确重新渲染预览。
export default memo(MaterialThumbnail);
