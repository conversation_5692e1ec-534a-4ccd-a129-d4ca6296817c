import React, { useState, useEffect, useCallback, useRef } from 'react';
import './custom-material-panel.css';
import { Slider } from '../slider/slider';
import { HexColorPicker } from 'react-colorful';
import { TextureUpload } from '../upload';
import { FILE_VALIDATION_CONFIGS } from '../../utils/fileUpload';

interface CustomMaterialPanelProps {
  onChange?: (material: MaterialSettings) => void;
  defaultSettings?: MaterialSettings | null;
}

export interface MaterialSettings {
  color: string; // color is a hex string
  metalness: number;
  roughness: number;
  opacity: number;
  textureUrl?: string;
}

export const CustomMaterialPanel: React.FC<CustomMaterialPanelProps> = ({
  onChange,
  defaultSettings,
}) => {
  const isInitialMount = useRef(true);
  const isInteracting = useRef(false);
  const interactionTimeout = useRef<NodeJS.Timeout | null>(null);
  const onChangeRef = useRef(onChange);

  useEffect(() => {
    onChangeRef.current = onChange;
  }, [onChange]);

  const [color, setColor] = useState(defaultSettings?.color || 'var(--color-material-default)');
  const [metalness, setMetalness] = useState(defaultSettings?.metalness ?? 0.5);
  const [roughness, setRoughness] = useState(defaultSettings?.roughness ?? 0.5);
  const [opacity, setOpacity] = useState(defaultSettings?.opacity ?? 1);
  const [textureUrl, setTextureUrl] = useState(defaultSettings?.textureUrl || '');
  // 移除了dragOver和textureInputRef，现在由TextureUpload组件处理

  useEffect(() => {
    if (isInitialMount.current) {
      isInitialMount.current = false;
      return;
    }
    if (isInteracting.current) {
      return;
    }
    if (defaultSettings) {
      setColor(defaultSettings.color || 'var(--color-material-default)');
      setMetalness(defaultSettings.metalness ?? 0.5);
      setRoughness(defaultSettings.roughness ?? 0.5);
      setOpacity(defaultSettings.opacity ?? 1);
      setTextureUrl(defaultSettings.textureUrl || '');
    }
  }, [defaultSettings]);

  const triggerChangeWithInteraction = useCallback((settings: Partial<MaterialSettings>) => {
    isInteracting.current = true;
    if (interactionTimeout.current) {
      clearTimeout(interactionTimeout.current);
    }

    if (onChangeRef.current) {
      const fullSettings: MaterialSettings = {
        color,
        metalness,
        roughness,
        opacity,
        textureUrl,
        ...settings,
      };
      onChangeRef.current(fullSettings);
    }

    interactionTimeout.current = setTimeout(() => {
      isInteracting.current = false;
    }, 500);
  }, [color, metalness, roughness, opacity, textureUrl, onChangeRef]);

  useEffect(() => {
    return () => {
      if (interactionTimeout.current) {
        clearTimeout(interactionTimeout.current);
      }
    };
  }, []);

  const handleColorChange = useCallback((newColor: string) => {
    setColor(newColor);
    triggerChangeWithInteraction({ color: newColor });
  }, [triggerChangeWithInteraction]);

  const handleMetalnessChange = useCallback((value: number) => {
    setMetalness(value);
    triggerChangeWithInteraction({ metalness: value });
  }, [triggerChangeWithInteraction]);

  const handleRoughnessChange = useCallback((value: number) => {
    setRoughness(value);
    triggerChangeWithInteraction({ roughness: value });
  }, [triggerChangeWithInteraction]);

  const handleOpacityChange = useCallback((value: number) => {
    setOpacity(value);
    triggerChangeWithInteraction({ opacity: value });
  }, [triggerChangeWithInteraction]);

  const handleTextureUpload = useCallback((file: File) => {
    const url = URL.createObjectURL(file);
    setTextureUrl(url);
    triggerChangeWithInteraction({ textureUrl: url });
  }, [triggerChangeWithInteraction]);

  const handleRemoveTexture = useCallback(() => {
    if (textureUrl) {
      URL.revokeObjectURL(textureUrl);
    }
    setTextureUrl('');
    triggerChangeWithInteraction({ textureUrl: '' });
  }, [textureUrl, triggerChangeWithInteraction]);

  return (
    <div className="custom-material-panel">
      <div className="form-group">
        <label>颜色</label>
        <div className="color-picker-container">
          <HexColorPicker color={color} onChange={handleColorChange} />
        </div>
      </div>
      <div className="form-group">
        <label>金属感: {Math.round(metalness * 100)}%</label>
        <Slider
          value={metalness}
          onChange={handleMetalnessChange}
          min={0}
          max={1}
          step={0.01}
        />
      </div>
      <div className="form-group">
        <label>粗糙度: {Math.round(roughness * 100)}%</label>
        <Slider
          value={roughness}
          onChange={handleRoughnessChange}
          min={0}
          max={1}
          step={0.01}
        />
      </div>
      <div className="form-group">
        <label>不透明度: {Math.round(opacity * 100)}%</label>
        <Slider
          value={opacity}
          onChange={handleOpacityChange}
          min={0}
          max={1}
          step={0.01}
        />
      </div>
      <div className="form-group">
        <label>纹理贴图</label>
        <TextureUpload
          onFileSelect={handleTextureUpload}
          validationOptions={FILE_VALIDATION_CONFIGS.IMAGE}
          onError={(error: string) => alert(error)}
          accept=".jpg,.jpeg,.png,.webp"
          previewUrl={textureUrl}
          showRemoveButton={!!textureUrl}
          onRemove={handleRemoveTexture}
          placeholder="点击或拖拽上传纹理"
          size="medium"
        />
      </div>
    </div>
  );
};