/* 表单基础样式 */
.form {
  width: 100%;
}

.form__fieldset {
  border: none;
  padding: 0;
  margin: 0;
  min-width: 0;
}

.form--disabled .form__fieldset {
  opacity: 0.6;
  pointer-events: none;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-group__label {
  display: block;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--color-content-regular);
  line-height: var(--line-height-tight);
}

.form-group__required {
  color: var(--color-error);
  margin-left: var(--spacing-xxs);
}

.form-group__control {
  position: relative;
}

.form-group__error {
  margin-top: var(--spacing-xs);
  font-size: var(--font-size-sm);
  color: var(--color-error);
  line-height: var(--line-height-tight);
}

.form-group__help {
  margin-top: var(--spacing-xs);
  font-size: var(--font-size-sm);
  color: var(--color-content-secondary);
  line-height: var(--line-height-tight);
}

.form-group--error .form-group__control input,
.form-group--error .form-group__control textarea,
.form-group--error .form-group__control select {
  border-color: var(--color-error);
}

.form-group--error .form-group__control input:focus,
.form-group--error .form-group__control textarea:focus,
.form-group--error .form-group__control select:focus {
  border-color: var(--color-error);
  box-shadow: 0 0 0 var(--outline-width) var(--color-error-alpha);
}

/* 表单操作区域 */
.form-actions {
  display: flex;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-xl);
  padding-top: var(--spacing-lg);
  border-top: var(--border-width) solid var(--color-border);
}

.form-actions--left {
  justify-content: flex-start;
}

.form-actions--center {
  justify-content: center;
}

.form-actions--right {
  justify-content: flex-end;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-actions {
    flex-direction: column;
  }
  
  .form-actions button {
    width: 100%;
  }
}
