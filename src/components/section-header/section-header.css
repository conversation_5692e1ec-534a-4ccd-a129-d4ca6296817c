.section-header {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.section-header__content {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  gap: var(--spacing-md);
}

.section-header__main {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  flex: 1;
  min-width: 0;
}

.section-header__icon {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.section-header__icon-svg {
  color: var(--color-content-accent);
}

.section-header__text {
  flex: 1;
  min-width: 0;
}

.section-header__title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-content-accent);
  margin: 0;
  line-height: var(--line-height-tight);
}

.section-header__subtitle {
  font-size: var(--font-size-sm);
  color: var(--color-content-secondary);
  margin: var(--spacing-xs) 0 0 0;
  line-height: var(--line-height-base);
}

.section-header__actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  flex-shrink: 0;
}

/* 尺寸变体 */
.section-header--small .section-header__title {
  font-size: var(--font-size-base);
}

.section-header--small .section-header__subtitle {
  font-size: var(--font-size-sm);
}

.section-header--small .section-header__icon-svg {
  width: var(--icon-size-small);
  height: var(--icon-size-small);
}

.section-header--medium .section-header__icon-svg {
  width: var(--icon-size-medium);
  height: var(--icon-size-medium);
}

.section-header--large .section-header__title {
  font-size: var(--font-size-xl);
}

.section-header--large .section-header__subtitle {
  font-size: var(--font-size-base);
}

.section-header--large .section-header__icon-svg {
  width: var(--icon-size-large);
  height: var(--icon-size-large);
}
