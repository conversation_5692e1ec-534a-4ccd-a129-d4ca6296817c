/* 模态框特定样式 - 基础样式已在components.css中定义 */

/* 模态框特定的表单和文件上传样式 - 基础样式已在components.css中定义 */


/* 模态框特定的模型和材质预览样式 - 基础样式已在components.css中定义 */

.material-preview {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-base);
}

/* 模态框中的预览球体特殊样式 */
.modal-container .preview-sphere {
  min-height: var(--spacing-giant); /* 确保预览区域有足够空间 */
}

/* --- Upload Status --- */
.upload-status {
  margin-top: var(--spacing-md);
  padding: var(--spacing-md);
  border: var(--border-width) solid var(--color-border);
  border-radius: var(--radius-base);
  background-color: var(--color-bg-overlay);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-base);
}

.upload-message {
  font-size: var(--font-size-sm);
  color: var(--color-content-secondary);
  margin: 0;
  text-align: center;
}

.progress-bar-container {
  width: 100%;
  height: var(--spacing-sm);
  background-color: var(--color-bg-hover);
  border-radius: var(--radius-xs);
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background-color: var(--color-brand);
  border-radius: var(--radius-xs);
  transition: width var(--animation-duration) ease-in-out;
}

.upload-percentage {
  font-size: var(--font-size-sm);
  color: var(--color-content-mute);
  text-align: center;
  font-weight: var(--font-weight-medium);
}