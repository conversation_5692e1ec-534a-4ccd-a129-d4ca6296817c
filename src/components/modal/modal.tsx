import React, { useRef } from 'react';
import { X } from 'lucide-react';
import { IconButton } from '../icon-button/icon-button';
import './modal.css';

export interface ModalProps {
  /** 是否显示模态框 */
  visible: boolean;
  /** 模态框标题 */
  title: string;
  /** 模态框内容 */
  children: React.ReactNode;
  /** 关闭回调 */
  onClose: () => void;
  /** 模态框尺寸，默认为 'medium' */
  size?: 'small' | 'medium' | 'large' | 'full';
  /** 自定义类名 */
  className?: string;
  /** 是否显示关闭按钮，默认为 true */
  showCloseButton?: boolean;
  /** 点击遮罩层是否关闭，默认为 true */
  closeOnOverlayClick?: boolean;
  /** 底部操作区域内容 */
  footer?: React.ReactNode;
}

export const Modal: React.FC<ModalProps> = ({
  visible,
  title,
  children,
  onClose,
  size = 'medium',
  className = '',
  showCloseButton = true,
  closeOnOverlayClick = true,
  footer,
}) => {
  const overlayRef = useRef<HTMLDivElement>(null);
  const mouseDownTarget = useRef<EventTarget | null>(null);

  if (!visible) return null;

  const handleMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    mouseDownTarget.current = e.target;
  };

  const handleMouseUp = (e: React.MouseEvent<HTMLDivElement>) => {
    if (
      mouseDownTarget.current === overlayRef.current &&
      e.target === overlayRef.current &&
      closeOnOverlayClick
    ) {
      onClose();
    }
    mouseDownTarget.current = null;
  };

  const modalClasses = [
    'modal-container',
    `modal-container--${size}`,
    className,
  ].filter(Boolean).join(' ');

  return (
    <div
      ref={overlayRef}
      className="modal-overlay"
      onMouseDown={handleMouseDown}
      onMouseUp={handleMouseUp}
    >
      <div className={modalClasses}>
        <div className="modal-header">
          <h2 className="modal-title">{title}</h2>
          {showCloseButton && (
            <IconButton 
              icon={X} 
              onClick={onClose} 
              size="small" 
              className="modal-close-button"
            />
          )}
        </div>
        <div className="modal-content">
          {children}
        </div>
        {footer && (
          <div className="modal-footer">
            {footer}
          </div>
        )}
      </div>
    </div>
  );
};

export default Modal;
