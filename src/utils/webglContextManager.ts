/**
 * WebGL上下文管理器
 * 用于统一管理WebGL上下文的创建、释放和监控，防止上下文丢失
 */

import * as THREE from 'three';

// 全局上下文计数器
let activeContextCount = 0;
const MAX_CONTEXTS = 16; // 增加最大上下文数量，以支持更多的材质缩略图
const contextRegistry = new Map<THREE.WebGLRenderer, {
  timestamp: number;
  recoveryAttempts: number;
}>();

// 自定义事件，用于通知上下文丢失和恢复
export const WebGLEvents = {
  CONTEXT_LOST: 'webgl-context-lost',
  CONTEXT_RESTORED: 'webgl-context-restored',
  CONTEXT_CREATED: 'webgl-context-created',
  CONTEXT_RELEASED: 'webgl-context-released'
};

/**
 * 注册新的WebGL上下文
 */
export function registerWebGLContext(gl: THREE.WebGLRenderer): boolean {
  // 检查是否已经注册过该上下文
  if (contextRegistry.has(gl)) {
    return true; // 已注册，直接返回成功
  }
  
  // 如果达到上限，尝试释放最旧的上下文
  if (activeContextCount >= MAX_CONTEXTS) {
    const released = forceReleaseOldestContext();
    if (!released) {
      console.warn(`WebGL context limit reached (${MAX_CONTEXTS}). Cannot create new context.`);
      return false;
    }
  }
  
  contextRegistry.set(gl, {
    timestamp: Date.now(),
    recoveryAttempts: 0
  });
  activeContextCount++;
  
  // 添加上下文丢失和恢复的事件监听
  const canvas = gl.domElement;
  
  // 使用命名函数而不是匿名函数，以便后续可以正确移除事件监听
  const contextLostHandler = (event: Event) => handleContextLost(event, gl);
  const contextRestoredHandler = () => handleContextRestored(gl);
  
  // 存储事件处理函数引用到renderer对象上，以便后续可以正确移除
  (gl as unknown as Record<string, unknown>).__contextLostHandler = contextLostHandler;
  (gl as unknown as Record<string, unknown>).__contextRestoredHandler = contextRestoredHandler;
  
  canvas.addEventListener('webglcontextlost', contextLostHandler);
  canvas.addEventListener('webglcontextrestored', contextRestoredHandler);
  
  // console.log(`WebGL context registered. Active contexts: ${activeContextCount}/${MAX_CONTEXTS}`);
  
  // 触发自定义事件
  window.dispatchEvent(new CustomEvent(WebGLEvents.CONTEXT_CREATED, { detail: { renderer: gl } }));
  
  return true;
}

/**
 * 注销WebGL上下文
 */
export function unregisterWebGLContext(gl: THREE.WebGLRenderer): void {
  if (contextRegistry.has(gl)) {
    try {
      // 移除事件监听
      const canvas = gl.domElement;
      
      // 使用存储的事件处理函数引用
      const glWithHandlers = gl as unknown as Record<string, unknown>;
      if (glWithHandlers.__contextLostHandler) {
        canvas.removeEventListener('webglcontextlost', glWithHandlers.__contextLostHandler as EventListener);
        delete glWithHandlers.__contextLostHandler;
      }

      if (glWithHandlers.__contextRestoredHandler) {
        canvas.removeEventListener('webglcontextrestored', glWithHandlers.__contextRestoredHandler as EventListener);
        delete glWithHandlers.__contextRestoredHandler;
      }
      
      contextRegistry.delete(gl);
      activeContextCount--;
      // console.log(`WebGL context unregistered. Active contexts: ${activeContextCount}/${MAX_CONTEXTS}`);
      
      // 触发自定义事件
      window.dispatchEvent(new CustomEvent(WebGLEvents.CONTEXT_RELEASED, { detail: { renderer: gl } }));
    } catch (error) {
      console.warn('Error during WebGL context unregistration:', error);
    }
  }
}

// 此处删除重复的safeReleaseContext函数

/**
 * 处理上下文丢失事件
 */
function handleContextLost(event: Event, gl: THREE.WebGLRenderer): void {
  event.preventDefault(); // 阻止默认行为
  console.warn('WebGL context lost, attempting to restore...');
  
  // 触发自定义事件
  window.dispatchEvent(new CustomEvent(WebGLEvents.CONTEXT_LOST, { detail: { renderer: gl } }));
}

/**
 * 处理上下文恢复事件
 */
function handleContextRestored(gl: THREE.WebGLRenderer): void {
  console.log('WebGL context restored');
  
  // 重置恢复尝试次数
  const contextInfo = contextRegistry.get(gl);
  if (contextInfo) {
    contextInfo.recoveryAttempts = 0;
  }
  
  // 触发自定义事件
  window.dispatchEvent(new CustomEvent(WebGLEvents.CONTEXT_RESTORED, { detail: { renderer: gl } }));
}

/**
 * 获取当前活跃的上下文数量
 */
export function getActiveContextCount(): number {
  return activeContextCount;
}

/**
 * 检查是否可以创建新的上下文
 */
export function canCreateNewContext(): boolean {
  return activeContextCount < MAX_CONTEXTS;
}

/**
 * 强制释放最旧的上下文（紧急情况下使用）
 * @returns {boolean} 是否成功释放了上下文
 */
export function forceReleaseOldestContext(): boolean {
  if (contextRegistry.size === 0) {
    return false;
  }
  
  // 找到最旧的上下文
  let oldestTime = Infinity;
  let oldestRenderer: THREE.WebGLRenderer | null = null;
  
  contextRegistry.forEach((info, renderer) => {
    if (info.timestamp < oldestTime) {
      oldestTime = info.timestamp;
      oldestRenderer = renderer;
    }
  });
  
  if (oldestRenderer) {
    try {
      safeReleaseContext(oldestRenderer);
      console.log('Forced release of oldest WebGL context');
      return true;
    } catch (error) {
      console.warn('Error forcing context release:', error);
      return false;
    }
  }
  
  return false;
}

/**
 * 安全地释放WebGL上下文
 */
export function safeReleaseContext(renderer: THREE.WebGLRenderer): void {
  if (!renderer) return;
  
  try {
    // 检查WebGL上下文是否已经丢失
    const gl = renderer.getContext();
    const isContextLost = gl && gl.isContextLost && gl.isContextLost();
    
    // 先注销上下文，确保事件监听器被移除
    unregisterWebGLContext(renderer);
    
    // 只有在上下文未丢失时才进行清理操作
    if (!isContextLost) {
      try {
        // 清理渲染列表
        renderer.renderLists.dispose();
        
        // 重置渲染目标
        renderer.setRenderTarget(null);
        
        // 清理场景
        renderer.clear();
        
        // 重置信息
        renderer.info.reset();
      } catch (e) {
        console.warn('Error cleaning up renderer resources:', e);
      }
      
      // 释放Three.js资源
       try {
         // 只有在上下文未丢失时才尝试强制丢失上下文
         // 再次检查上下文状态，因为在清理过程中可能已经丢失
         const currentGl = renderer.getContext();
         if (currentGl && !currentGl.isContextLost()) {
           try {
             renderer.forceContextLoss();
           } catch {
             // 忽略不支持的错误或上下文已丢失的错误
           }
         }
       } catch (e) {
         console.warn('Error forcing context loss:', e);
       }
    }
    
    // 无论上下文是否丢失，都要调用dispose清理资源
    try {
      renderer.dispose();
    } catch (e) {
      console.warn('Error disposing renderer:', e);
    }
  } catch (error) {
    console.warn('Error during context release:', error);
  }
}

/**
 * 检查浏览器的WebGL支持情况
 */
export function checkWebGLSupport(): { supported: boolean; reason?: string } {
  try {
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
    
    if (!gl) {
      return { supported: false, reason: 'WebGL not supported by this browser' };
    }
    
    return { supported: true };
  } catch (e) {
    return { supported: false, reason: 'Error checking WebGL support: ' + e };
  }
}

/**
 * 优化渲染器配置，根据设备性能调整
 */
export function optimizeRenderer(renderer: THREE.WebGLRenderer): void {
  // 检测设备性能
  const isLowPowerDevice = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
  
  // 根据设备性能调整渲染器设置
  if (isLowPowerDevice) {
    renderer.setPixelRatio(1);
    renderer.shadowMap.enabled = false;
    renderer.setSize(renderer.domElement.clientWidth, renderer.domElement.clientHeight, false);
  } else {
    const pixelRatio = Math.min(window.devicePixelRatio, 2); // 限制最大像素比为2
    renderer.setPixelRatio(pixelRatio);
    renderer.shadowMap.enabled = true;
  }
  
  // 统一所有渲染器的基本设置，确保渲染效果一致
  renderer.outputColorSpace = THREE.SRGBColorSpace;
  renderer.toneMapping = THREE.ACESFilmicToneMapping;
  renderer.toneMappingExposure = 1;
}
