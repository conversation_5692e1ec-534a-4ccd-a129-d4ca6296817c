import * as THREE from 'three';

/**
 * Three.js 相关的工具函数
 */

// 全局纹理加载器和缓存
const textureLoader = new THREE.TextureLoader();
const textureCache = new Map<string, THREE.Texture>();

/**
 * 纹理加载函数，带缓存机制和错误处理
 */
export const loadTexture = (url: string): THREE.Texture | null => {
  if (!url) return null;

  if (textureCache.has(url)) {
    return textureCache.get(url)!;
  }

  try {
    const texture = textureLoader.load(
      url,
      // onLoad
      () => {
        console.log(`Texture loaded successfully: ${url}`);
      },
      // onProgress
      undefined,
      // onError
      (error) => {
        console.error(`Failed to load texture: ${url}`, error);
        textureCache.delete(url); // 从缓存中移除失败的纹理
      }
    );
    
    // 设置纹理参数以提高性能
    texture.generateMipmaps = true;
    texture.minFilter = THREE.LinearMipmapLinearFilter;
    texture.magFilter = THREE.LinearFilter;
    texture.wrapS = THREE.RepeatWrapping;
    texture.wrapT = THREE.RepeatWrapping;
    
    textureCache.set(url, texture);
    return texture;
  } catch (error) {
    console.error(`Error creating texture loader for: ${url}`, error);
    return null;
  }
};

/**
 * 清理纹理缓存
 */
export const clearTextureCache = () => {
  textureCache.forEach((texture) => {
    texture.dispose();
  });
  textureCache.clear();
};

/**
 * 清理特定纹理
 */
export const clearTexture = (url: string) => {
  const texture = textureCache.get(url);
  if (texture) {
    texture.dispose();
    textureCache.delete(url);
  }
};

/**
 * 获取纹理缓存大小
 */
export const getTextureCacheSize = () => {
  return textureCache.size;
};

/**
 * 标准化模型尺寸和位置
 */
export const normalizeModel = (scene: THREE.Object3D, targetSize: number = 2) => {
  // 重置变换
  scene.position.set(0, 0, 0);
  scene.rotation.set(0, 0, 0);
  scene.scale.set(1, 1, 1);

  // 计算包围盒尺寸
  const box = new THREE.Box3().setFromObject(scene);
  const size = new THREE.Vector3();
  box.getSize(size);
  const maxDim = Math.max(size.x, size.y, size.z);

  // 计算缩放系数
  const scale = targetSize / maxDim;
  scene.scale.setScalar(scale);

  // 重新计算包围盒并居中
  box.setFromObject(scene);
  const center = new THREE.Vector3();
  box.getCenter(center);
  scene.position.set(-center.x, -center.y, -center.z);
};

/**
 * 创建标准材质
 */
export const createStandardMaterial = (options: {
  color: string;
  metalness: number;
  roughness: number;
  opacity: number;
  textureUrl?: string;
}): THREE.MeshStandardMaterial => {
  const { color, metalness, roughness, opacity, textureUrl } = options;
  
  const material = new THREE.MeshStandardMaterial({
    color: new THREE.Color(color),
    metalness,
    roughness,
    transparent: opacity < 1,
    opacity,
    map: textureUrl ? loadTexture(textureUrl) : null,
    // 确保所有材质都有一致的基础属性
    emissive: new THREE.Color(0x000000),
    emissiveIntensity: 0,
    // 统一光照相关属性
    envMapIntensity: 1,
    flatShading: false,
    wireframe: false
  });
  
  // 存储原始发光值，用于高亮状态切换
  material.userData.originalEmissive = new THREE.Color(0x000000);
  
  material.needsUpdate = true;
  return material;
};

/**
 * 安全地释放材质资源
 */
export const disposeMaterial = (material: THREE.Material) => {
  if (material instanceof THREE.MeshStandardMaterial) {
    // 释放纹理（如果不在缓存中）
    if (material.map && !textureCache.has(material.map.source.data?.src || '')) {
      material.map.dispose();
    }
    if (material.normalMap && !textureCache.has(material.normalMap.source.data?.src || '')) {
      material.normalMap.dispose();
    }
    if (material.roughnessMap && !textureCache.has(material.roughnessMap.source.data?.src || '')) {
      material.roughnessMap.dispose();
    }
    if (material.metalnessMap && !textureCache.has(material.metalnessMap.source.data?.src || '')) {
      material.metalnessMap.dispose();
    }
  }
  
  material.dispose();
};

/**
 * 安全地释放几何体资源
 */
export const disposeGeometry = (geometry: THREE.BufferGeometry) => {
  geometry.dispose();
};

/**
 * 递归释放对象及其子对象的资源
 */
export const disposeObject3D = (object: THREE.Object3D) => {
  object.traverse((child) => {
    if (child instanceof THREE.Mesh) {
      if (child.geometry) {
        disposeGeometry(child.geometry);
      }
      if (child.material) {
        if (Array.isArray(child.material)) {
          child.material.forEach(disposeMaterial);
        } else {
          disposeMaterial(child.material);
        }
      }
    }
  });
};

/**
 * WebGL上下文丢失处理
 */
export const forceContextLoss = (renderer: THREE.WebGLRenderer) => {
  try {
    renderer.dispose();
    const gl = renderer.getContext();
    const loseCtxExt = gl?.getExtension('WEBGL_lose_context');
    if (loseCtxExt) {
      loseCtxExt.loseContext();
    }
  } catch (error) {
    console.warn('Failed to force WebGL context loss:', error);
  }
};

/**
 * 3D性能监控器
 */
export class ThreePerformanceMonitor {
  private renderer: THREE.WebGLRenderer;
  private stats: {
    drawCalls: number;
    triangles: number;
    points: number;
    lines: number;
    frameTime: number;
    memoryUsage: number;
  } = {
    drawCalls: 0,
    triangles: 0,
    points: 0,
    lines: 0,
    frameTime: 0,
    memoryUsage: 0
  };

  constructor(renderer: THREE.WebGLRenderer) {
    this.renderer = renderer;
  }

  startFrame() {
    this.stats.frameTime = performance.now();
  }

  endFrame() {
    const info = this.renderer.info;
    this.stats.drawCalls = info.render.calls;
    this.stats.triangles = info.render.triangles;
    this.stats.points = info.render.points;
    this.stats.lines = info.render.lines;
    this.stats.frameTime = performance.now() - this.stats.frameTime;

    // 估算内存使用（基于纹理和几何体）
    this.stats.memoryUsage = info.memory.geometries + info.memory.textures;

    // 性能警告
    if (this.stats.frameTime > 16.67) { // 60fps threshold
      console.warn(`Frame time exceeded 16.67ms: ${this.stats.frameTime.toFixed(2)}ms`);
    }

    if (this.stats.drawCalls > 100) {
      console.warn(`High draw calls: ${this.stats.drawCalls}`);
    }
  }

  getStats() {
    return { ...this.stats };
  }

  reset() {
    this.renderer.info.reset();
  }
}

/**
 * 材质优化器
 */
export class MaterialOptimizer {
  private static materialCache = new Map<string, THREE.Material>();

  /**
   * 获取或创建优化的材质
   */
  static getOptimizedMaterial(
    key: string,
    factory: () => THREE.Material
  ): THREE.Material {
    if (this.materialCache.has(key)) {
      return this.materialCache.get(key)!;
    }

    const material = factory();
    this.materialCache.set(key, material);
    return material;
  }

  /**
   * 批量优化场景中的材质
   */
  static optimizeSceneMaterials(scene: THREE.Object3D) {
    const materialMap = new Map<string, THREE.Material>();

    scene.traverse((object) => {
      if (object instanceof THREE.Mesh && object.material) {
        const material = Array.isArray(object.material)
          ? object.material[0]
          : object.material;

        if (material instanceof THREE.MeshStandardMaterial) {
          const key = this.getMaterialKey(material);

          if (materialMap.has(key)) {
            // 复用相同的材质
            object.material = materialMap.get(key)!;
          } else {
            // 优化材质设置
            this.optimizeMaterial(material);
            materialMap.set(key, material);
          }
        }
      }
    });

    console.log(`Material optimization: ${materialMap.size} unique materials`);
  }

  private static getMaterialKey(material: THREE.MeshStandardMaterial): string {
    return [
      material.color.getHexString(),
      material.metalness,
      material.roughness,
      material.opacity,
      material.transparent,
      material.map?.uuid || 'no-map'
    ].join('|');
  }

  private static optimizeMaterial(material: THREE.MeshStandardMaterial) {
    // 禁用不必要的特性以提高性能
    if (material.opacity >= 1) {
      material.transparent = false;
    }

    // 优化纹理设置
    if (material.map) {
      material.map.generateMipmaps = true;
      material.map.minFilter = THREE.LinearMipmapLinearFilter;
      material.map.magFilter = THREE.LinearFilter;
    }
  }

  static clearCache() {
    this.materialCache.forEach(material => material.dispose());
    this.materialCache.clear();
  }
}

/**
 * 几何体优化器
 */
export class GeometryOptimizer {
  /**
   * 优化几何体
   */
  static optimizeGeometry(geometry: THREE.BufferGeometry): THREE.BufferGeometry {
    // 合并重复顶点 - 使用兼容的方法
    geometry = geometry.clone();
    
    // 注意：mergeVertices 在新版 Three.js 中不是 BufferGeometry 的方法
    // 使用 BufferGeometryUtils.mergeVertices 代替
    // 这里我们只进行其他优化
    
    // 计算法线（如果没有）
    if (!geometry.attributes.normal) {
      geometry.computeVertexNormals();
    }

    // 计算包围盒和包围球
    geometry.computeBoundingBox();
    geometry.computeBoundingSphere();

    return geometry;
  }

  /**
   * 批量优化场景中的几何体
   */
  static optimizeSceneGeometries(scene: THREE.Object3D) {
    let optimizedCount = 0;

    scene.traverse((object) => {
      if (object instanceof THREE.Mesh && object.geometry) {
        const originalVertices = object.geometry.attributes.position?.count || 0;
        object.geometry = this.optimizeGeometry(object.geometry);
        const newVertices = object.geometry.attributes.position?.count || 0;

        if (originalVertices !== newVertices) {
          optimizedCount++;
        }
      }
    });

    console.log(`Geometry optimization: ${optimizedCount} geometries optimized`);
  }
}

/**
 * LOD (Level of Detail) 管理器
 */
export class LODManager {
  private lodObjects = new Map<THREE.Object3D, THREE.LOD>();

  /**
   * 为对象创建LOD
   */
  createLOD(
    object: THREE.Object3D,
    distances: number[] = [0, 50, 100]
  ): THREE.LOD {
    const lod = new THREE.LOD();

    // 添加原始对象（最高质量）
    lod.addLevel(object, distances[0]);

    // 创建简化版本
    for (let i = 1; i < distances.length; i++) {
      const simplified = this.createSimplifiedVersion(object, i);
      lod.addLevel(simplified, distances[i]);
    }

    this.lodObjects.set(object, lod);
    return lod;
  }

  private createSimplifiedVersion(object: THREE.Object3D, level: number): THREE.Object3D {
    const simplified = object.clone();

    simplified.traverse((child) => {
      if (child instanceof THREE.Mesh) {
        // 简化几何体（这里是简单的示例，实际可能需要更复杂的算法）
        const scale = 1 / (level + 1);
        child.scale.setScalar(scale);

        // 可以在这里添加更复杂的几何体简化逻辑
      }
    });

    return simplified;
  }

  /**
   * 更新LOD
   */
  update(camera: THREE.Camera) {
    this.lodObjects.forEach((lod) => {
      lod.update(camera);
    });
  }

  /**
   * 清理LOD对象
   */
  dispose() {
    this.lodObjects.forEach((lod) => {
      lod.traverse((child) => {
        if (child instanceof THREE.Mesh) {
          if (child.geometry) disposeGeometry(child.geometry);
          if (child.material) {
            if (Array.isArray(child.material)) {
              child.material.forEach(disposeMaterial);
            } else {
              disposeMaterial(child.material);
            }
          }
        }
      });
    });
    this.lodObjects.clear();
  }
}

/**
 * 高级模型优化器
 */
export class AdvancedModelOptimizer {
  /**
   * 智能材质合并
   */
  static mergeSimilarMaterials(
    scene: THREE.Object3D
  ): number {
    const materialGroups = new Map<string, THREE.Material[]>();
    let mergedCount = 0;

    // 收集所有材质
    scene.traverse((object) => {
      if (object instanceof THREE.Mesh && object.material) {
        const material = Array.isArray(object.material)
          ? object.material[0]
          : object.material;

        if (material instanceof THREE.MeshStandardMaterial) {
          const key = this.getMaterialSignature(material);
          if (!materialGroups.has(key)) {
            materialGroups.set(key, []);
          }
          materialGroups.get(key)!.push(material);
        }
      }
    });

    // 合并相似材质
    materialGroups.forEach((group) => {
      if (group.length > 1) {
        const baseMaterial = group[0] as THREE.MeshStandardMaterial;
        const mergedMaterial = baseMaterial.clone();

        // 应用合并的材质到所有使用相似材质的对象
        scene.traverse((object) => {
          if (object instanceof THREE.Mesh && object.material) {
            const material = Array.isArray(object.material)
              ? object.material[0]
              : object.material;

            if (group.includes(material)) {
              object.material = mergedMaterial;
              mergedCount++;
            }
          }
        });

        // 清理旧材质
        group.slice(1).forEach(material => material.dispose());
      }
    });

    console.log(`Merged ${mergedCount} similar materials`);
    return mergedCount;
  }

  private static getMaterialSignature(material: THREE.MeshStandardMaterial): string {
    return [
      Math.round(material.color.r * 10) / 10,
      Math.round(material.color.g * 10) / 10,
      Math.round(material.color.b * 10) / 10,
      Math.round(material.metalness * 10) / 10,
      Math.round(material.roughness * 10) / 10,
      material.transparent ? 1 : 0
    ].join('|');
  }

  /**
   * 几何体实例化
   */
  static createInstancedGeometry(
    scene: THREE.Object3D,
    minInstances: number = 3
  ): number {
    const geometryGroups = new Map<string, THREE.Mesh[]>();
    let instancedCount = 0;

    // 按几何体分组
    scene.traverse((object) => {
      if (object instanceof THREE.Mesh) {
        const key = this.getGeometrySignature(object.geometry);
        if (!geometryGroups.has(key)) {
          geometryGroups.set(key, []);
        }
        geometryGroups.get(key)!.push(object);
      }
    });

    // 创建实例化几何体
    geometryGroups.forEach((meshes) => {
      if (meshes.length >= minInstances) {
        const baseMesh = meshes[0];
        const instancedMesh = new THREE.InstancedMesh(
          baseMesh.geometry,
          baseMesh.material,
          meshes.length
        );

        // 设置实例变换矩阵
        meshes.forEach((mesh, index) => {
          instancedMesh.setMatrixAt(index, mesh.matrix);
        });

        instancedMesh.instanceMatrix.needsUpdate = true;

        // 替换原始网格
        const parent = meshes[0].parent;
        if (parent) {
          parent.add(instancedMesh);
          meshes.forEach(mesh => parent.remove(mesh));
          instancedCount += meshes.length;
        }
      }
    });

    console.log(`Created instanced geometry for ${instancedCount} objects`);
    return instancedCount;
  }

  private static getGeometrySignature(geometry: THREE.BufferGeometry): string {
    const position = geometry.attributes.position;
    const vertexCount = position ? position.count : 0;
    const hasNormal = !!geometry.attributes.normal;
    const hasUv = !!geometry.attributes.uv;

    return `${vertexCount}|${hasNormal}|${hasUv}`;
  }

  /**
   * 纹理图集优化
   */
  static optimizeTextureAtlas(
    scene: THREE.Object3D,
    atlasSize: number = 2048
  ): THREE.Texture | null {
    const textures: THREE.Texture[] = [];
    const materials: THREE.MeshStandardMaterial[] = [];

    // 收集所有纹理
    scene.traverse((object) => {
      if (object instanceof THREE.Mesh && object.material) {
        const material = Array.isArray(object.material)
          ? object.material[0]
          : object.material;

        if (material instanceof THREE.MeshStandardMaterial && material.map) {
          textures.push(material.map);
          materials.push(material);
        }
      }
    });

    if (textures.length < 2) {
      return null;
    }

    // 创建图集（简化实现）
    const canvas = document.createElement('canvas');
    canvas.width = atlasSize;
    canvas.height = atlasSize;
    const ctx = canvas.getContext('2d');

    if (!ctx) return null;

    // 这里应该实现更复杂的纹理打包算法
    // 简化版本：将纹理平铺排列

    textures.forEach(() => {
      // 这里需要将Three.js纹理转换为Canvas可用的图像
      // 实际实现会更复杂
    });

    const atlasTexture = new THREE.CanvasTexture(canvas);

    // 更新材质UV坐标以使用图集
    materials.forEach((material) => {
      material.map = atlasTexture;
      // 这里需要更新UV坐标
    });

    console.log(`Created texture atlas with ${textures.length} textures`);
    return atlasTexture;
  }

  /**
   * 自适应质量调整
   */
  static createAdaptiveQualityManager(
    renderer: THREE.WebGLRenderer,
    targetFPS: number = 60
  ) {
    let frameCount = 0;
    let lastTime = performance.now();
    let currentQuality = 1.0;

    const qualityLevels = [0.5, 0.75, 1.0, 1.25];
    let currentLevelIndex = 2; // 从1.0开始

    return {
      update: () => {
        frameCount++;
        const currentTime = performance.now();

        if (currentTime - lastTime >= 1000) { // 每秒检查一次
          const fps = frameCount;
          frameCount = 0;
          lastTime = currentTime;

          // 根据FPS调整质量
          if (fps < targetFPS * 0.8 && currentLevelIndex > 0) {
            // 降低质量
            currentLevelIndex--;
            currentQuality = qualityLevels[currentLevelIndex];
            renderer.setPixelRatio(window.devicePixelRatio * currentQuality);
            console.log(`Quality reduced to ${currentQuality}`);
          } else if (fps > targetFPS * 1.1 && currentLevelIndex < qualityLevels.length - 1) {
            // 提高质量
            currentLevelIndex++;
            currentQuality = qualityLevels[currentLevelIndex];
            renderer.setPixelRatio(window.devicePixelRatio * currentQuality);
            console.log(`Quality increased to ${currentQuality}`);
          }
        }
      },

      getCurrentQuality: () => currentQuality,

      setQuality: (quality: number) => {
        const index = qualityLevels.findIndex(level => level >= quality);
        if (index !== -1) {
          currentLevelIndex = index;
          currentQuality = qualityLevels[index];
          renderer.setPixelRatio(window.devicePixelRatio * currentQuality);
        }
      }
    };
  }
}
