/**
 * 全面的错误处理工具
 */

export enum ErrorType {
  NETWORK = 'NETWORK',
  VALIDATION = 'VALIDATION',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  NOT_FOUND = 'NOT_FOUND',
  SERVER = 'SERVER',
  CLIENT = 'CLIENT',
  WEBGL = 'WEBGL',
  MODEL_LOADING = 'MODEL_LOADING',
  MATERIAL = 'MATERIAL',
  SHADER = 'SHADER',
  UNKNOWN = 'UNKNOWN'
}

export interface AppError {
  type: ErrorType;
  message: string;
  code?: string;
  details?: unknown;
  timestamp: number;
  stack?: string;
}

/**
 * 创建应用错误
 */
export const createAppError = (
  type: ErrorType,
  message: string,
  code?: string,
  details?: unknown
): AppError => ({
  type,
  message,
  code,
  details,
  timestamp: Date.now(),
  stack: new Error().stack
});

/**
 * 错误分类器
 */
export const classifyError = (error: unknown): AppError => {
  if (error instanceof Error) {
    // 网络错误
    if (error.message.includes('fetch') || error.message.includes('network')) {
      return createAppError(ErrorType.NETWORK, '网络连接失败，请检查网络设置', 'NETWORK_ERROR', error);
    }
    
    // 认证错误
    if (error.message.includes('401') || error.message.includes('Unauthorized')) {
      return createAppError(ErrorType.AUTHENTICATION, '认证失败，请重新登录', 'AUTH_ERROR', error);
    }
    
    // 权限错误
    if (error.message.includes('403') || error.message.includes('Forbidden')) {
      return createAppError(ErrorType.AUTHORIZATION, '权限不足，无法执行此操作', 'PERMISSION_ERROR', error);
    }
    
    // 404错误
    if (error.message.includes('404') || error.message.includes('Not Found')) {
      return createAppError(ErrorType.NOT_FOUND, '请求的资源不存在', 'NOT_FOUND_ERROR', error);
    }
    
    // 服务器错误
    if (error.message.includes('500') || error.message.includes('Internal Server Error')) {
      return createAppError(ErrorType.SERVER, '服务器内部错误，请稍后重试', 'SERVER_ERROR', error);
    }
    
    // 客户端错误
    if (error.message.includes('400') || error.message.includes('Bad Request')) {
      return createAppError(ErrorType.CLIENT, '请求参数错误', 'CLIENT_ERROR', error);
    }

    // WebGL错误
    if (error.message.includes('WebGL') || error.message.includes('context') ||
        error.message.includes('CONTEXT_LOST_WEBGL')) {
      return createAppError(ErrorType.WEBGL, 'WebGL上下文错误，请刷新页面重试', 'WEBGL_ERROR', error);
    }

    // 模型加载错误
    if (error.message.includes('GLTF') || error.message.includes('model') ||
        error.message.includes('loader')) {
      return createAppError(ErrorType.MODEL_LOADING, '模型加载失败，请检查文件格式', 'MODEL_ERROR', error);
    }

    // 材质错误
    if (error.message.includes('material') || error.message.includes('texture')) {
      return createAppError(ErrorType.MATERIAL, '材质处理错误', 'MATERIAL_ERROR', error);
    }

    // Shader错误
    if (error.message.includes('shader') || error.message.includes('uniform') ||
        error.message.includes('compilation')) {
      return createAppError(ErrorType.SHADER, 'Shader编译错误', 'SHADER_ERROR', error);
    }
    
    return createAppError(ErrorType.UNKNOWN, error.message, 'UNKNOWN_ERROR', error);
  }
  
  if (typeof error === 'string') {
    return createAppError(ErrorType.UNKNOWN, error, 'STRING_ERROR');
  }
  
  return createAppError(ErrorType.UNKNOWN, '发生未知错误', 'UNKNOWN_ERROR', error);
};

/**
 * 错误恢复策略
 */
export interface RecoveryStrategy {
  canRecover: (error: AppError) => boolean;
  recover: (error: AppError) => Promise<void> | void;
  maxRetries?: number;
  retryDelay?: number;
}

/**
 * 自动重试策略
 */
export const createRetryStrategy = (
  maxRetries: number = 3,
  retryDelay: number = 1000,
  shouldRetry: (error: AppError) => boolean = (error) => error.type === ErrorType.NETWORK
): RecoveryStrategy => ({
  canRecover: shouldRetry,
  recover: async (error: AppError) => {
    console.log(`Retrying operation after error: ${error.message}`);
    await new Promise(resolve => setTimeout(resolve, retryDelay));
  },
  maxRetries,
  retryDelay
});

/**
 * WebGL上下文恢复策略
 */
export const createWebGLRecoveryStrategy = (): RecoveryStrategy => ({
  canRecover: (error: AppError) => error.type === ErrorType.WEBGL,
  recover: async () => {
    console.log('Attempting WebGL context recovery...');
    // 清理纹理缓存
    if (typeof window !== 'undefined' && (window as unknown).clearTextureCache) {
      (window as unknown).clearTextureCache();
    }
    // 强制垃圾回收
    if (typeof window !== 'undefined' && (window as unknown).gc) {
      (window as unknown).gc();
    }
    // 等待一段时间让浏览器恢复
    await new Promise(resolve => setTimeout(resolve, 2000));
  },
  maxRetries: 2
});

/**
 * 模型加载恢复策略
 */
export const createModelLoadingRecoveryStrategy = (): RecoveryStrategy => ({
  canRecover: (error: AppError) => error.type === ErrorType.MODEL_LOADING,
  recover: async () => {
    console.log('Attempting model loading recovery...');
    // 清理模型缓存
    if (typeof window !== 'undefined' && (window as unknown).useGLTF?.clear) {
      // 这需要在具体的组件中实现
    }
    await new Promise(resolve => setTimeout(resolve, 1000));
  },
  maxRetries: 2
});

/**
 * 错误恢复管理器
 */
export class ErrorRecoveryManager {
  private strategies: RecoveryStrategy[] = [];
  private retryCount = new Map<string, number>();

  addStrategy(strategy: RecoveryStrategy) {
    this.strategies.push(strategy);
  }

  async handleError(error: AppError, operationId?: string): Promise<boolean> {
    const applicableStrategy = this.strategies.find(strategy => strategy.canRecover(error));
    
    if (!applicableStrategy) {
      return false;
    }

    const retryKey = operationId || error.code || 'default';
    const currentRetries = this.retryCount.get(retryKey) || 0;
    const maxRetries = applicableStrategy.maxRetries || 3;

    if (currentRetries >= maxRetries) {
      this.retryCount.delete(retryKey);
      return false;
    }

    try {
      await applicableStrategy.recover(error);
      this.retryCount.set(retryKey, currentRetries + 1);
      return true;
    } catch (recoveryError) {
      console.error('Error recovery failed:', recoveryError);
      this.retryCount.delete(retryKey);
      return false;
    }
  }

  resetRetryCount(operationId: string) {
    this.retryCount.delete(operationId);
  }

  clearAllRetries() {
    this.retryCount.clear();
  }
}

/**
 * 全局错误恢复管理器实例
 */
export const globalErrorRecovery = new ErrorRecoveryManager();

// 添加默认的重试策略
globalErrorRecovery.addStrategy(createRetryStrategy(3, 1000, (error) =>
  error.type === ErrorType.NETWORK || error.type === ErrorType.SERVER
));

// 添加WebGL恢复策略
globalErrorRecovery.addStrategy(createWebGLRecoveryStrategy());

// 添加模型加载恢复策略
globalErrorRecovery.addStrategy(createModelLoadingRecoveryStrategy());

/**
 * 安全执行函数，带错误处理和恢复
 */
export const safeExecute = async <T>(
  operation: () => Promise<T>,
  operationId?: string,
  customErrorHandler?: (error: AppError) => void
): Promise<T | null> => {
  try {
    return await operation();
  } catch (error) {
    const appError = classifyError(error);
    
    if (customErrorHandler) {
      customErrorHandler(appError);
    }
    
    const recovered = await globalErrorRecovery.handleError(appError, operationId);
    
    if (recovered) {
      // 重试操作
      try {
        return await operation();
      } catch (retryError) {
        const retryAppError = classifyError(retryError);
        if (customErrorHandler) {
          customErrorHandler(retryAppError);
        }
        return null;
      }
    }
    
    return null;
  }
};

/**
 * 错误边界装饰器
 */
export const withErrorBoundary = <T extends (...args: unknown[]) => unknown>(
  fn: T,
  errorHandler?: (error: AppError) => void
): T => {
  return ((...args: unknown[]) => {
    try {
      const result = fn(...args);
      
      // 如果是Promise，处理异步错误
      if (result instanceof Promise) {
        return result.catch((error) => {
          const appError = classifyError(error);
          if (errorHandler) {
            errorHandler(appError);
          } else {
            console.error('Unhandled async error:', appError);
          }
          throw appError;
        });
      }
      
      return result;
    } catch (error) {
      const appError = classifyError(error);
      if (errorHandler) {
        errorHandler(appError);
      } else {
        console.error('Unhandled sync error:', appError);
      }
      throw appError;
    }
  }) as T;
};
