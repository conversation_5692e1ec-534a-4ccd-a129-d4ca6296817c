/**
 * 性能优化工具
 * 用于监控和优化WebGL性能
 */

import * as THREE from 'three';


// 全局性能监控状态
let isMonitoringActive = false;
let monitoringInterval: number | null = null;
const performanceHistory: Array<{timestamp: number, fps: number, memory?: number}> = [];

/**
 * 启动性能监控
 * @param callback 回调函数，用于接收性能数据
 * @param interval 监控间隔（毫秒）
 */
export function startPerformanceMonitoring(
  callback?: (data: {fps: number, memory?: number}) => void,
  interval: number = 1000
): void {
  if (isMonitoringActive) return;
  
  let lastTime = performance.now();
  let frames = 0;
  
  // 使用更高效的监控方式，避免过于频繁的回调
  const monitor = () => {
    const now = performance.now();
    frames++;
    
    if (now >= lastTime + interval) {
      const fps = Math.round((frames * 1000) / (now - lastTime));
      
      // 收集内存使用情况（如果可用）
      let memory: number | undefined = undefined;
      try {
        // 扩展 Performance 接口以包含 memory 属性
        interface PerformanceMemory {
          usedJSHeapSize: number;
          jsHeapSizeLimit: number;
          totalJSHeapSize?: number;
        }
        
        interface ExtendedPerformance extends Performance {
          memory?: PerformanceMemory;
        }
        
        const perf = performance as unknown as ExtendedPerformance;
        if (perf.memory && perf.memory.jsHeapSizeLimit > 0) {
          memory = Math.round((perf.memory.usedJSHeapSize / perf.memory.jsHeapSizeLimit) * 100);
        }
      } catch (error) {
        // 某些浏览器可能会限制对内存API的访问
        console.warn('Memory API access error:', error);
      }
      
      // 记录性能历史
      performanceHistory.push({
        timestamp: now,
        fps,
        memory
      });
      
      // 只保留最近100条记录
      if (performanceHistory.length > 100) {
        performanceHistory.shift();
      }
      
      // 调用回调函数
      if (callback) {
        callback({fps, memory});
      }
      
      // 重置计数器
      frames = 0;
      lastTime = now;
    }
    
    // 只有在监控仍然活跃时才继续
    if (isMonitoringActive) {
      monitoringInterval = requestAnimationFrame(monitor);
    }
  };
  
  // 启动监控循环
  isMonitoringActive = true;
  monitoringInterval = requestAnimationFrame(monitor);
  isMonitoringActive = true;
}

/**
 * 停止性能监控
 */
export function stopPerformanceMonitoring(): void {
  if (!isMonitoringActive || monitoringInterval === null) return;
  
  cancelAnimationFrame(monitoringInterval);
  monitoringInterval = null;
  isMonitoringActive = false;
}

/**
 * 获取性能历史数据
 */
export function getPerformanceHistory(): Array<{timestamp: number, fps: number, memory?: number}> {
  return [...performanceHistory];
}

/**
 * 清理性能历史数据
 */
export function clearPerformanceHistory(): void {
  performanceHistory.length = 0;
}

/**
 * 自动优化WebGL渲染器设置
 * @param renderer Three.js WebGL渲染器
 * @param targetFps 目标帧率
 */
export function autoOptimizeRenderer(renderer: THREE.WebGLRenderer, targetFps: number = 30): void {
  if (!renderer) return;
  
  // 获取最近的性能数据
  const recentPerformance = performanceHistory.slice(-10);
  if (recentPerformance.length < 5) return; // 需要足够的数据才能做决策
  
  // 计算平均帧率
  const avgFps = recentPerformance.reduce((sum, item) => sum + item.fps, 0) / recentPerformance.length;
  
  // 如果帧率低于目标，进行优化
  if (avgFps < targetFps) {
    // 降低像素比
    const currentPixelRatio = renderer.getPixelRatio();
    if (currentPixelRatio > 1) {
      renderer.setPixelRatio(Math.max(1, currentPixelRatio - 0.5));
    }
    
    // 禁用阴影
    if (renderer.shadowMap.enabled) {
      renderer.shadowMap.enabled = false;
    }
    
    // 使用更简单的色调映射
    if (renderer.toneMapping !== THREE.NoToneMapping) {
      renderer.toneMapping = THREE.NoToneMapping;
    }
    
    console.log(`Performance optimization applied: Average FPS ${avgFps.toFixed(1)} -> Target ${targetFps}`);
  }
}

/**
 * 检测并处理WebGL上下文丢失
 * @param renderer Three.js WebGL渲染器
 */
export function setupContextLossHandling(renderer: THREE.WebGLRenderer): () => void {
  if (!renderer) return () => {};
  
  const canvas = renderer.domElement;
  let contextLostCount = 0;
  let lastContextLostTime = 0;
  
  const handleContextLost = (event: Event) => {
    event.preventDefault();
    const now = Date.now();
    
    // 记录上下文丢失事件
    contextLostCount++;
    
    // 如果短时间内频繁丢失上下文，可能是硬件问题
    if (now - lastContextLostTime < 5000 && contextLostCount > 3) {
      console.warn('Frequent WebGL context loss detected. Switching to low power mode.');
      
      // 尝试降低渲染质量
      renderer.setPixelRatio(1);
      renderer.shadowMap.enabled = false;
      renderer.toneMapping = THREE.NoToneMapping;
    }
    
    lastContextLostTime = now;
  };
  
  const handleContextRestored = () => {
    console.log('WebGL context restored');
    // 重置计数器
    if (Date.now() - lastContextLostTime > 10000) {
      contextLostCount = 0;
    }
  };
  
  canvas.addEventListener('webglcontextlost', handleContextLost);
  canvas.addEventListener('webglcontextrestored', handleContextRestored);
  
  // 返回清理函数
  return () => {
    canvas.removeEventListener('webglcontextlost', handleContextLost);
    canvas.removeEventListener('webglcontextrestored', handleContextRestored);
  };
}

/**
 * 在模型切换时清理WebGL资源
 * @param renderer Three.js WebGL渲染器
 */
export function cleanupWebGLResources(renderer: THREE.WebGLRenderer): void {
  if (!renderer) return;
  
  try {
    // 清理渲染列表
    renderer.renderLists.dispose();
    
    // 清理渲染目标
    renderer.setRenderTarget(null);
    
    // 清理场景
    renderer.clear();
    
    // 强制完成所有待处理的渲染
    renderer.info.reset();
    
  // 触发垃圾回收
  interface WindowWithGC extends Window {
    gc?: () => void;
  }
  const gc = (window as unknown as WindowWithGC).gc;
  if (typeof gc === 'function') {
    setTimeout(() => {
      gc();
    }, 0);
  }
  } catch (error) {
    console.warn('Error during WebGL resources cleanup:', error);
  }
}

/**
 * 检测浏览器是否支持WebGL2
 */
export function detectWebGL2Support(): boolean {
  try {
    const canvas = document.createElement('canvas');
    return !!canvas.getContext('webgl2');
  } catch (error) {
    console.warn('WebGL2 not supported:', error);
    return false;
  }
}

/**
 * 检测设备是否为低性能设备
 */
export function isLowPerformanceDevice(): boolean {
  // 检查是否为移动设备
  const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
  
  // 检查处理器核心数
  const cpuCores = navigator.hardwareConcurrency || 1;
  
  // 检查设备内存（如果可用）
  let lowMemory = false;
  // 扩展 Navigator 接口以包含 deviceMemory 属性
  interface NavigatorWithMemory extends Navigator {
    deviceMemory?: number;
  }
  
  const nav = navigator as unknown as NavigatorWithMemory;
  if ('deviceMemory' in nav && nav.deviceMemory) {
    lowMemory = nav.deviceMemory < 4;
  }
  
  return isMobile || cpuCores <= 2 || lowMemory;
}
