import { useState, useMemo, useCallback } from 'react';

export interface SearchOptions<T> {
  /** 搜索字段列表 */
  searchFields: (keyof T)[];
  /** 是否区分大小写 */
  caseSensitive?: boolean;
  /** 自定义过滤函数 */
  customFilter?: (item: T, query: string) => boolean;
  /** 防抖延迟（毫秒） */
  debounceMs?: number;
}

export interface SearchState<T> {
  /** 当前搜索查询 */
  query: string;
  /** 设置搜索查询 */
  setQuery: (query: string) => void;
  /** 过滤后的数据 */
  filteredData: T[];
  /** 清空搜索 */
  clearSearch: () => void;
  /** 是否有搜索查询 */
  hasQuery: boolean;
}

/**
 * 通用的搜索过滤Hook
 * 支持多字段搜索、自定义过滤逻辑等
 */
export const useSearch = <T extends Record<string, unknown>>(
  data: T[],
  options: SearchOptions<T>
): SearchState<T> => {
  const {
    searchFields,
    caseSensitive = false,
    customFilter
  } = options;

  const [query, setQuery] = useState('');

  const filteredData = useMemo(() => {
    if (!query.trim()) {
      return data;
    }

    const searchTerm = caseSensitive ? query : query.toLowerCase();

    return data.filter(item => {
      // 如果提供了自定义过滤函数，优先使用
      if (customFilter) {
        return customFilter(item, query);
      }

      // 默认的多字段搜索逻辑
      return searchFields.some(field => {
        const fieldValue = item[field];
        
        if (fieldValue == null) {
          return false;
        }

        const stringValue = String(fieldValue);
        const searchValue = caseSensitive ? stringValue : stringValue.toLowerCase();
        
        return searchValue.includes(searchTerm);
      });
    });
  }, [data, query, searchFields, caseSensitive, customFilter]);

  const clearSearch = useCallback(() => {
    setQuery('');
  }, []);

  const hasQuery = query.trim().length > 0;

  return {
    query,
    setQuery,
    filteredData,
    clearSearch,
    hasQuery
  };
};
