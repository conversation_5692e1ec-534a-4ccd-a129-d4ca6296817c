import { useState, useCallback } from 'react';

export interface ConfirmDialogConfig {
  title: string;
  message: string;
  type?: 'info' | 'warning' | 'error' | 'success';
  confirmText?: string;
  cancelText?: string;
  confirmVariant?: 'primary' | 'danger';
  showCancel?: boolean;
}

export interface ConfirmDialogState extends ConfirmDialogConfig {
  visible: boolean;
  onConfirm: () => void;
  onCancel: () => void;
}

/**
 * 通用的确认对话框管理Hook
 * 提供简单的API来显示确认对话框并处理用户响应
 */
export const useConfirmDialog = () => {
  const [dialogState, setDialogState] = useState<ConfirmDialogState>({
    visible: false,
    title: '',
    message: '',
    type: 'info',
    confirmText: '确定',
    cancelText: '取消',
    confirmVariant: 'primary',
    showCancel: true,
    onConfirm: () => {},
    onCancel: () => {}
  });

  const showConfirm = useCallback((
    config: ConfirmDialogConfig,
    onConfirm?: () => void | Promise<void>,
    onCancel?: () => void
  ) => {
    return new Promise<boolean>((resolve) => {
      const handleConfirm = async () => {
        try {
          if (onConfirm) {
            await onConfirm();
          }
          setDialogState(prev => ({ ...prev, visible: false }));
          resolve(true);
        } catch (error) {
          console.error('Confirm action failed:', error);
          // 不关闭对话框，让用户重试或取消
        }
      };

      const handleCancel = () => {
        if (onCancel) {
          onCancel();
        }
        setDialogState(prev => ({ ...prev, visible: false }));
        resolve(false);
      };

      setDialogState({
        visible: true,
        title: config.title,
        message: config.message,
        type: config.type || 'info',
        confirmText: config.confirmText || '确定',
        cancelText: config.cancelText || '取消',
        confirmVariant: config.confirmVariant || 'primary',
        showCancel: config.showCancel !== false,
        onConfirm: handleConfirm,
        onCancel: handleCancel
      });
    });
  }, []);

  const hideDialog = useCallback(() => {
    setDialogState(prev => ({ ...prev, visible: false }));
  }, []);

  return {
    dialogState,
    showConfirm,
    hideDialog
  };
};
