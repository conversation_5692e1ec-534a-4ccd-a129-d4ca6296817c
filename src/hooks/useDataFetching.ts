import { useState, useEffect, useCallback } from 'react';

export interface DataFetchingState<T> {
  data: T[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  updateItem: (id: string | number, updater: (item: T) => T) => void;
  removeItem: (id: string | number) => void;
  addItem: (item: T) => void;
}

export interface DataFetchingOptions<T> {
  /** 数据获取函数 */
  fetchFn: () => Promise<T[]>;
  /** 获取单个项目的ID字段名 */
  idField?: keyof T;
  /** 是否在组件挂载时自动获取数据 */
  immediate?: boolean;
  /** 数据排序函数 */
  sortFn?: (a: T, b: T) => number;
}

/**
 * 通用的数据获取和管理Hook
 * 提供数据获取、更新、删除等常用操作
 */
export const useDataFetching = <T extends Record<string, unknown>>(
  options: DataFetchingOptions<T>
): DataFetchingState<T> => {
  const {
    fetchFn,
    idField = 'id' as keyof T,
    immediate = true,
    sortFn
  } = options;

  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await fetchFn();
      let sortedData = result;
      
      if (sortFn) {
        sortedData = [...result].sort(sortFn);
      }
      
      setData(sortedData);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '数据获取失败';
      setError(errorMessage);
      console.error('Data fetching error:', err);
    } finally {
      setLoading(false);
    }
  }, [fetchFn, sortFn]);

  const updateItem = useCallback((id: string | number, updater: (item: T) => T) => {
    setData(prevData => {
      const newData = prevData.map(item => {
        if (String(item[idField]) === String(id)) {
          return updater(item);
        }
        return item;
      });
      
      return sortFn ? [...newData].sort(sortFn) : newData;
    });
  }, [idField, sortFn]);

  const removeItem = useCallback((id: string | number) => {
    setData(prevData => 
      prevData.filter(item => String(item[idField]) !== String(id))
    );
  }, [idField]);

  const addItem = useCallback((item: T) => {
    setData(prevData => {
      const newData = [...prevData, item];
      return sortFn ? newData.sort(sortFn) : newData;
    });
  }, [sortFn]);

  useEffect(() => {
    if (immediate) {
      fetchData();
    }
  }, [fetchData, immediate]);

  return {
    data,
    loading,
    error,
    refetch: fetchData,
    updateItem,
    removeItem,
    addItem
  };
};
