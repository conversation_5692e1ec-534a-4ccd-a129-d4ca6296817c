# 会通材质管理系统

一个现代化的3D材质管理和预览系统，基于React + Three.js构建。

## 🚀 快速开始

### 环境要求

- Node.js 18+
- PostgreSQL 12+
- npm

### 本地开发

```bash
# 1. 克隆项目
git clone <repository-url>
cd huitong-material

# 2. 安装依赖
npm install

# 3. 配置环境变量
cp .env.example .env.development
# 编辑 .env.development 文件，配置数据库连接

# 4. 初始化数据库
npm run db:push

# 5. 启动开发服务器
npm run dev
```

### 访问地址

- 前端: http://localhost:5173
- 后端API: http://localhost:3001

## 🚂 Railway 部署

### 一键部署

1. 将项目推送到 GitHub 仓库
2. 在 Railway 中连接你的 GitHub 仓库
3. Railway 会自动检测并使用项目中的配置文件进行部署

### 环境变量配置

在 Railway 项目设置中配置以下环境变量：

```bash
# 数据库配置 (Railway PostgreSQL 插件会自动提供)
POSTGRES_PRISMA_URL=postgresql://username:password@host:port/database?schema=public
POSTGRES_URL_NON_POOLING=postgresql://username:password@host:port/database?schema=public

# 应用配置
NODE_ENV=production
PORT=3000

# 文件上传配置
UPLOAD_DIR=./backend/uploads
```

### 数据库设置

1. 在 Railway 中添加 PostgreSQL 插件
2. 数据库连接信息会自动注入到环境变量中
3. 部署时会自动运行数据库迁移

## 🛠️ 技术栈

- **前端**: React 19 + TypeScript + Vite
- **3D渲染**: Three.js + React Three Fiber
- **后端**: Node.js + Express
- **数据库**: PostgreSQL + Prisma ORM
- **样式**: CSS Modules

## 📝 可用脚本

```bash
npm run dev          # 启动开发服务器
npm run build        # 构建生产版本
npm run start        # 启动生产服务器
npm run db:generate  # 生成Prisma客户端
npm run db:push      # 推送数据库变更
npm run db:migrate   # 部署数据库迁移
npm run db:studio    # 打开数据库管理界面
npm run lint         # 代码检查
npm run clean        # 清理缓存文件
npm run setup        # 环境设置
npm run check:env    # 环境检查
npm run deploy:railway # Railway部署助手
```

## 📁 项目结构

```
src/
├── components/     # React组件
├── pages/         # 页面组件
├── hooks/         # 自定义Hooks
├── services/      # API服务
└── utils/         # 工具函数

backend/
├── server.mjs     # 服务器入口
└── uploads/       # 文件上传目录

prisma/
├── schema.prisma  # 数据库模式
└── migrations/    # 数据库迁移

scripts/
├── setup-env.js   # 环境设置脚本
├── check-env.js   # 环境检查脚本
└── clean.js       # 清理脚本
```

## 🔧 开发指南

### 环境配置

1. 复制 `.env.example` 到 `.env.development`
2. 配置数据库连接信息
3. 运行 `npm run setup` 自动设置环境

### 数据库操作

```bash
# 生成Prisma客户端
npm run db:generate

# 推送数据库变更
npm run db:push

# 打开数据库管理界面
npm run db:studio
```

### 开发工具

```bash
# 环境检查
npm run check:env

# 清理缓存
npm run clean

# 安全启动（带环境检查）
npm run dev:safe
```

## 🚀 Railway 部署

### 方式一：GitHub 自动部署（推荐）

1. **推送代码到 GitHub**
```bash
git add .
git commit -m "准备部署"
git push origin main
```

2. **在 Railway 中关联 GitHub 仓库**
   - 访问 [railway.app](https://railway.app)
   - 点击 "New Project" → "Deploy from GitHub repo"
   - 选择您的仓库并点击 "Deploy Now"
   - 添加 PostgreSQL 数据库

3. **运行数据库迁移**
```bash
railway run npx prisma migrate deploy
```

详细步骤请查看：[GitHub 自动部署指南](./GITHUB_AUTO_DEPLOY.md)

### 方式二：CLI 手动部署

1. **安装 Railway CLI**
```bash
npm install -g @railway/cli
```

2. **运行部署助手**
```bash
npm run deploy:railway
```

3. **按照提示完成部署**

详细步骤请查看：[Railway 部署指南](./RAILWAY_DEPLOYMENT.md)

### 环境变量配置

在 Railway 项目中设置以下环境变量：

- `POSTGRES_PRISMA_URL` - PostgreSQL 连接 URL (Railway 自动提供)
- `POSTGRES_URL_NON_POOLING` - PostgreSQL 直连 URL (Railway 自动提供)
- `NODE_ENV=production` (Railway 自动设置)
- `PORT` (Railway 自动设置)

### 部署文件说明

- `railway.json` - Railway 项目配置
- `nixpacks.toml` - 构建配置
- `.railwayignore` - 部署时忽略的文件

## 📄 许可证

MIT License
