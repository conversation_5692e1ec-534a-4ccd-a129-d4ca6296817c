# 会通材质管理系统

一个现代化的3D材质管理和预览系统，基于React + Three.js构建。

## 🚀 快速开始

### 环境要求

- Node.js 18+
- PostgreSQL 12+
- npm

### 本地开发

```bash
# 1. 克隆项目
git clone <repository-url>
cd huitong-material

# 2. 安装依赖
npm install

# 3. 配置环境变量
cp .env.example .env.development
# 编辑 .env.development 文件，配置数据库连接

# 4. 初始化数据库
npm run db:push

# 5. 启动开发服务器
npm run dev
```

### 访问地址

- 前端: http://localhost:5173
- 后端API: http://localhost:3001

## 🚀 本地部署

### 环境变量配置

配置以下环境变量：

```bash
# 数据库配置
POSTGRES_PRISMA_URL=postgresql://username:password@host:port/database?schema=public
POSTGRES_URL_NON_POOLING=postgresql://username:password@host:port/database?schema=public

# 应用配置
NODE_ENV=production
PORT=3001

# 文件上传配置
UPLOAD_DIR=./backend/uploads
```

### 数据库设置

1. 安装并配置 PostgreSQL 数据库
2. 创建数据库实例
3. 运行数据库迁移：`npm run db:push`

## 🛠️ 技术栈

- **前端**: React 19 + TypeScript + Vite
- **3D渲染**: Three.js + React Three Fiber
- **后端**: Node.js + Express
- **数据库**: PostgreSQL + Prisma ORM
- **样式**: CSS Modules

## 📝 可用脚本

```bash
npm run dev          # 启动开发服务器
npm run build        # 构建生产版本
npm run start        # 启动生产服务器
npm run db:generate  # 生成Prisma客户端
npm run db:push      # 推送数据库变更
npm run db:migrate   # 部署数据库迁移
npm run db:studio    # 打开数据库管理界面
npm run lint         # 代码检查
npm run clean        # 清理缓存文件
npm run setup        # 环境设置
npm run check:env    # 环境检查

```

## 📁 项目结构

```
src/
├── components/     # React组件
├── pages/         # 页面组件
├── hooks/         # 自定义Hooks
├── services/      # API服务
└── utils/         # 工具函数

backend/
├── server.mjs     # 服务器入口
└── uploads/       # 文件上传目录

prisma/
├── schema.prisma  # 数据库模式
└── migrations/    # 数据库迁移

scripts/
├── setup-env.js   # 环境设置脚本
├── check-env.js   # 环境检查脚本
└── clean.js       # 清理脚本
```

## 🔧 开发指南

### 环境配置

1. 复制 `.env.example` 到 `.env.development`
2. 配置数据库连接信息
3. 运行 `npm run setup` 自动设置环境

### 数据库操作

```bash
# 生成Prisma客户端
npm run db:generate

# 推送数据库变更
npm run db:push

# 打开数据库管理界面
npm run db:studio
```

### 开发工具

```bash
# 环境检查
npm run check:env

# 清理缓存
npm run clean

# 安全启动（带环境检查）
npm run dev:safe
```

## 🚀 生产部署

### 阿里云ECS部署（推荐）

#### 快速部署
```bash
# 1. 连接到ECS服务器
ssh root@your-server-ip

# 2. 下载并运行自动部署脚本
wget https://raw.githubusercontent.com/Bavoch/huitong-material/aliyun-deploy/deploy/aliyun-deploy.sh
chmod +x aliyun-deploy.sh
export DB_PASSWORD='your_secure_password'
./aliyun-deploy.sh
```

#### Docker Compose部署
```bash
# 1. 克隆项目
git clone -b aliyun-deploy https://github.com/Bavoch/huitong-material.git
cd huitong-material

# 2. 配置环境变量
cp .env.production.example .env.production
# 编辑 .env.production 设置数据库密码等

# 3. 启动服务
docker-compose -f deploy/docker-compose.aliyun.yml up -d
```

详细部署指南请查看：[阿里云ECS部署文档](./deploy/README.md)

### 本地生产环境

#### 构建生产版本
```bash
npm run build
```

#### 启动生产服务器
```bash
npm start
```

#### Docker 部署
```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 环境变量配置

生产环境需要配置以下环境变量：

```bash
# 数据库配置
POSTGRES_PRISMA_URL=postgresql://username:password@host:port/database?schema=public
POSTGRES_URL_NON_POOLING=postgresql://username:password@host:port/database?schema=public

# 应用配置
NODE_ENV=production
PORT=3001

# 文件上传配置
UPLOAD_DIR=./backend/uploads

# 安全配置（生产环境）
CORS_ORIGIN=https://your-domain.com
```

## 📄 许可证

MIT License
