# Node.js
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 环境变量文件
.env
.env.local
.env.development
.env.test
.env.production.local

# 日志文件
logs
*.log

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 覆盖率目录
coverage
.nyc_output

# 依赖目录
.npm
.eslintcache

# 可选的npm缓存目录
.npm

# 可选的REPL历史
.node_repl_history

# 输出的二进制文件
*.tgz

# Yarn完整性文件
.yarn-integrity

# dotenv环境变量文件
.env

# parcel-bundler缓存
.cache
.parcel-cache

# next.js构建输出
.next

# nuxt.js构建输出
.nuxt

# vuepress构建输出
.vuepress/dist

# Serverless目录
.serverless

# FuseBox缓存
.fusebox/

# DynamoDB本地文件
.dynamodb/

# Git
.git
.gitignore

# Docker
Dockerfile
docker-compose*.yml
.dockerignore

# IDE
.vscode
.idea
*.swp
*.swo
*~

# 构建输出
dist
build

# OS
.DS_Store
Thumbs.db

# Git hooks
.husky
.github

# 测试文件
test
tests
__tests__
*.test.js
*.spec.js

# 文档
README.md
CHANGELOG.md
LICENSE
*.md

# 部署脚本
deploy.sh
scripts/

# 临时文件
tmp
temp