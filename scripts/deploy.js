#!/usr/bin/env node

/**
 * 部署脚本 - 用于Railway部署时的数据库初始化
 */

import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

async function deploy() {
    console.log('🚀 开始部署流程...');
    
    try {
        // 检查数据库连接
        console.log('📊 检查数据库连接...');
        await execAsync('npx prisma db push --accept-data-loss');
        console.log('✅ 数据库连接成功');
        
        // 生成Prisma客户端
        console.log('🔧 生成Prisma客户端...');
        await execAsync('npx prisma generate');
        console.log('✅ Prisma客户端生成完成');
        
        console.log('🎉 部署流程完成！');
    } catch (error) {
        console.error('❌ 部署失败:', error.message);
        process.exit(1);
    }
}

// 只在生产环境执行
if (process.env.NODE_ENV === 'production') {
    deploy();
} else {
    console.log('⚠️  非生产环境，跳过部署脚本');
}