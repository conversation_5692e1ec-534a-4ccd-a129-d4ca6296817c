# 生产环境配置模板
# 在 Railway 中配置这些环境变量

# 数据库配置 (Railway PostgreSQL 插件会自动提供)
POSTGRES_PRISMA_URL=postgresql://username:password@host:port/database?schema=public
POSTGRES_URL_NON_POOLING=postgresql://username:password@host:port/database?schema=public

# 应用配置
NODE_ENV=production
PORT=3000

# 文件上传配置
UPLOAD_DIR=./backend/uploads

# 可选：如果需要自定义数据库连接
# POSTGRES_USER=your_username
# POSTGRES_PASSWORD=your_password
# POSTGRES_DB=huitong_material
# POSTGRES_HOST=your_host
# POSTGRES_PORT=5432