version: '3.8'

services:
  db:
    image: postgres:15
    container_name: huitong_db
    restart: always
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-password}
      POSTGRES_DB: ${POSTGRES_DB:-huitong}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - huitong_network

  backend:
    build:
      context: .
      dockerfile: Dockerfile
      target: backend

    command: ["tail", "-f", "/dev/null"]
    container_name: huitong_backend
    restart: always
    depends_on:
      - db
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER:-user}:${POSTGRES_PASSWORD:-password}@db:5432/${POSTGRES_DB:-huitong}?schema=public
      - NODE_ENV=production
    networks:
      - huitong_network

  frontend:
    build:
      context: .
      dockerfile: Dockerfile
      target: frontend
    command: ["tail", "-f", "/dev/null"]
    container_name: huitong_frontend
    restart: always
    networks:
      - huitong_network

  nginx:
    build:
      context: .
      dockerfile: Dockerfile.nginx
    container_name: huitong_nginx
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - frontend
      - backend
    networks:
      - huitong_network

volumes:
  postgres_data:
  frontend-dist:
    # 用于存储前端构建文件
    name: frontend-dist

networks:
  huitong_network:
    driver: bridge
