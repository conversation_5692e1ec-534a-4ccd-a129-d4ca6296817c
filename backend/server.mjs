import dotenv from 'dotenv';
import express from 'express';
import cors from 'cors';
import multer from 'multer';
import { PrismaClient } from '@prisma/client';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';

// Correctly define __dirname for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables based on NODE_ENV
if (process.env.NODE_ENV !== 'production') {
    // In development, load from .env.development file
    const envPath = path.resolve(__dirname, '../.env.development');
    if (fs.existsSync(envPath)) {
        dotenv.config({ path: envPath, override: true });
    } else {
        console.warn('⚠️  Development environment file not found, using system environment variables');
    }
}

// Initialize Prisma Client with enhanced configuration
const prisma = new PrismaClient({
    log: process.env.NODE_ENV === 'development' ? ['query', 'info', 'warn', 'error'] : ['error'],
    errorFormat: 'pretty',
});

// Database connection health check
async function checkDatabaseConnection() {
    try {
        await prisma.$connect();
        console.log('✅ Database connected successfully');
        return true;
    } catch (error) {
        console.error('❌ Database connection failed:', error.message);
        return false;
    }
}

// Graceful database disconnection
async function disconnectDatabase() {
    try {
        await prisma.$disconnect();
        console.log('✅ Database disconnected gracefully');
    } catch (error) {
        console.error('❌ Error disconnecting database:', error.message);
    }
}
const app = express();

// Middleware
app.use(cors());
app.use(express.json());
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// Health check endpoint for Railway
app.get('/health', (req, res) => {
    res.status(200).json({ 
        status: 'ok', 
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV || 'development'
    });
});

// Serve static files in production
if (process.env.NODE_ENV === 'production') {
    app.use(express.static(path.join(__dirname, '../dist')));
    
    // Handle React Router
    app.get('*', (req, res) => {
        if (!req.path.startsWith('/api') && !req.path.startsWith('/uploads') && !req.path.startsWith('/health')) {
            res.sendFile(path.join(__dirname, '../dist/index.html'));
        }
    });
}

// Multer setup for file uploads with enhanced security and cross-platform compatibility
const storage = multer.diskStorage({
    destination: (req, file, cb) => {
        // Use environment variable or default to backend/uploads
        const uploadDir = process.env.UPLOAD_DIR || path.join(__dirname, 'uploads');
        const dir = path.resolve(uploadDir);

        // Ensure directory exists with proper permissions
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true, mode: 0o755 });
        }
        cb(null, dir);
    },
    filename: (req, file, cb) => {
        // Sanitize filename to prevent security issues
        const sanitizedName = file.originalname
            .replace(/[^a-zA-Z0-9.\-_]/g, '_') // Replace unsafe characters
            .replace(/_{2,}/g, '_') // Replace multiple underscores with single
            .toLowerCase();

        const timestamp = Date.now();
        const randomSuffix = Math.random().toString(36).substring(2, 8);
        const filename = `${timestamp}-${randomSuffix}-${sanitizedName}`;

        cb(null, filename);
    },
});

// Configure multer with file size limits and type validation
const upload = multer({
    storage,
    limits: {
        fileSize: 100 * 1024 * 1024, // 100MB limit
        files: 1 // Only one file at a time
    },
    fileFilter: (req, file, cb) => {
        // Allow specific file types for 3D models and images
        const allowedTypes = [
            'model/gltf-binary', // .glb
            'model/gltf+json',   // .gltf
            'image/png',
            'image/jpeg',
            'image/jpg',
            'image/webp'
        ];

        const allowedExtensions = ['.glb', '.gltf', '.png', '.jpg', '.jpeg', '.webp'];
        const fileExtension = path.extname(file.originalname).toLowerCase();

        if (allowedTypes.includes(file.mimetype) || allowedExtensions.includes(fileExtension)) {
            cb(null, true);
        } else {
            cb(new Error(`不支持的文件类型: ${file.mimetype || fileExtension}`), false);
        }
    }
});

// Helper function to safely delete a file with enhanced security
const deleteFile = (filePath) => {
    if (!filePath) return;

    // Normalize path to prevent directory traversal attacks
    const normalizedPath = path.normalize(filePath);

    // Ensure the file is within the uploads directory
    const uploadDir = process.env.UPLOAD_DIR || path.join(__dirname, 'uploads');
    const uploadsPath = path.resolve(uploadDir);

    let fullPath;
    if (path.isAbsolute(normalizedPath)) {
        fullPath = normalizedPath;
    } else {
        // Handle relative paths that start with /uploads/
        if (normalizedPath.startsWith('/uploads/')) {
            fullPath = path.join(uploadsPath, normalizedPath.replace('/uploads/', ''));
        } else {
            fullPath = path.join(uploadsPath, normalizedPath);
        }
    }

    // Security check: ensure the resolved path is within uploads directory
    const resolvedPath = path.resolve(fullPath);
    if (!resolvedPath.startsWith(uploadsPath)) {
        console.error(`Security violation: Attempted to delete file outside uploads directory: ${resolvedPath}`);
        return;
    }

    if (fs.existsSync(resolvedPath)) {
        try {
            fs.unlinkSync(resolvedPath);
            console.log(`Successfully deleted file: ${resolvedPath}`);
        } catch (err) {
            console.error(`Error deleting file ${resolvedPath}:`, err);
        }
    } else {
        console.warn(`File not found for deletion: ${resolvedPath}`);
    }
};

// --- API Routes ---



// Endpoint to handle file uploads with enhanced error handling
app.post('/api/upload', (req, res) => {
    upload.single('file')(req, res, (err) => {
        if (err instanceof multer.MulterError) {
            if (err.code === 'LIMIT_FILE_SIZE') {
                return res.status(400).json({ error: '文件大小超过限制 (100MB)' });
            }
            return res.status(400).json({ error: `上传错误: ${err.message}` });
        } else if (err) {
            return res.status(400).json({ error: err.message });
        }

        if (!req.file) {
            return res.status(400).json({ error: '没有上传文件' });
        }

        // Use forward slashes for web URLs (cross-platform compatible)
        const filePath = `/uploads/${req.file.filename}`;

        res.status(200).json({
            url: filePath,
            pathname: filePath,
            filePath: filePath,
            size: req.file.size,
            originalName: req.file.originalname,
            mimeType: req.file.mimetype
        });
    });
});

// --- Model CRUD ---

// GET all models
app.get('/api/models', async (req, res) => {
    try {
        const models = await prisma.model.findMany({
            orderBy: { createdAt: 'desc' },
        });
        res.json(models);
    } catch (error) {
        console.error('Failed to fetch models:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// GET a single model by ID
app.get('/api/models/:id', async (req, res) => {
    const modelId = parseInt(req.params.id, 10);
    if (isNaN(modelId)) {
        return res.status(400).json({ error: 'Invalid model ID.' });
    }
    try {
        const model = await prisma.model.findUnique({ where: { id: modelId } });
        if (!model) {
            return res.status(404).json({ error: 'Model not found' });
        }
        res.json(model);
    } catch (error) {
        console.error(`Failed to fetch model with id: ${req.params.id}:`, error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// POST a new model
app.post('/api/models', async (req, res) => {
    const { name, filePath, thumbnailPath, size } = req.body;
    if (!name || !filePath) {
        return res.status(400).json({ error: 'Name and filePath are required.' });
    }
    try {
        const fileType = path.extname(filePath).slice(1).toUpperCase();
        const newModel = await prisma.model.create({
            data: {
                name,
                filePath,
                thumbnailPath: thumbnailPath || null,
                size: size ? String(size) : null, // Save size as a string
                fileType, // Save the file type
            },
        });
        res.status(201).json(newModel);
    } catch (error) {
        console.error('Failed to create model record:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// PUT to update a model
app.put('/api/models/:id', async (req, res) => {
    const modelId = parseInt(req.params.id, 10);
    if (isNaN(modelId)) {
        return res.status(400).json({ error: 'Invalid model ID.' });
    }
    const { name, filePath, thumbnailPath, size } = req.body;

    const dataToUpdate = {};

    if (name !== undefined) dataToUpdate.name = name;
    if (thumbnailPath !== undefined) dataToUpdate.thumbnailPath = thumbnailPath;
    if (size !== undefined) dataToUpdate.size = String(size);

    if (filePath) {
        dataToUpdate.filePath = filePath;
        dataToUpdate.fileType = path.extname(filePath).slice(1).toUpperCase();
    }

    if (Object.keys(dataToUpdate).length === 0) {
        return res.status(400).json({ error: 'No update data provided.' });
    }

    try {
        const updatedModel = await prisma.model.update({
            where: { id: modelId },
            data: dataToUpdate,
        });
        res.json(updatedModel);
    } catch (error) {
        console.error(`Failed to update model ${modelId}:`, error);
        if (error.code === 'P2025') {
            return res.status(404).json({ error: 'Model not found' });
        }
        res.status(500).json({ error: 'Internal server error' });
    }
});

// DELETE a model
app.delete('/api/models/:id', async (req, res) => {
    const modelId = parseInt(req.params.id, 10);
    if (isNaN(modelId)) {
        return res.status(400).json({ error: 'Invalid model ID.' });
    }
    try {
        const model = await prisma.model.findUnique({ where: { id: modelId } });
        if (model) {
            deleteFile(model.filePath);
            deleteFile(model.thumbnailPath);
        }
        await prisma.model.delete({ where: { id: modelId } });
        res.status(204).send();
    } catch (error) {
        console.error(`Failed to delete model ${req.params.id}:`, error);
        if (error.code === 'P2025') {
            return res.status(404).json({ error: 'Model not found' });
        }
        res.status(500).json({ error: 'Internal server error' });
    }
});

// --- Material CRUD ---

// GET all materials
app.get('/api/materials', async (req, res) => {
    try {
        const materials = await prisma.material.findMany({
            orderBy: { createdAt: 'desc' },
        });
        res.json(materials);
    } catch (error) {
        console.error('Failed to fetch materials:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// GET all materials for a specific model
app.get('/api/models/:model_id/materials', async (req, res) => {
    const modelId = parseInt(req.params.model_id, 10);
    if (isNaN(modelId)) {
        return res.status(400).json({ error: 'Invalid model ID.' });
    }
    try {
        const materials = await prisma.material.findMany({
            where: { modelId: modelId },
            orderBy: { createdAt: 'desc' },
        });
        res.json(materials);
    } catch (error) {
        console.error(`Failed to fetch materials for model ${modelId}:`, error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// POST a new material
app.post('/api/materials', async (req, res) => {
    const { model_id, name, data, thumbnailPath } = req.body;
    const modelId = parseInt(model_id, 10);

    if (!modelId || !name) {
        return res.status(400).json({ error: 'model_id and name are required.' });
    }
    if (isNaN(modelId)) {
        return res.status(400).json({ error: 'Invalid model_id.' });
    }

    try {
        const newMaterial = await prisma.material.create({
            data: {
                modelId: modelId,
                name: name,
                data: data || {},
                thumbnailPath: thumbnailPath || null,
            },
        });
        res.status(201).json(newMaterial);
    } catch (error) {
        console.error('Failed to add material:', error);
        res.status(500).json({ error: 'Failed to add material.', details: error.message });
    }
});

// DELETE a material
app.delete('/api/materials/:id', async (req, res) => {
    const { id } = req.params;
    try {
        const material = await prisma.material.findUnique({ where: { id: id } });
        if (material) {
            deleteFile(material.thumbnailPath);
        }
        await prisma.material.delete({ where: { id: id } });
        res.status(204).send();
    } catch (error) {
        console.error(`Failed to delete material ${id}:`, error);
        if (error.code === 'P2025') {
            return res.status(404).json({ error: 'Material not found' });
        }
        res.status(500).json({ error: 'Internal server error' });
    }
});

// --- Server Start ---

export default app;

// 启动服务器
const PORT = process.env.PORT || 3001;

// Initialize server with database connection check
async function startServer() {
    // Check database connection before starting server
    const dbConnected = await checkDatabaseConnection();
    if (!dbConnected) {
        console.error('❌ Cannot start server without database connection');
        process.exit(1);
    }

    const server = app.listen(PORT, () => {
        console.log(`🚀 Backend server is running on http://localhost:${PORT}`);
        console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
        console.log(`🗄️  Database: Connected`);
        console.log(`📁 Upload directory: ${process.env.UPLOAD_DIR || path.join(__dirname, 'uploads')}`);
    });

    // Enhanced graceful shutdown handling
    const gracefulShutdown = async (signal) => {
        console.log(`🛑 ${signal} received, shutting down gracefully`);

        // Close server first
        server.close(async () => {
            console.log('🔌 HTTP server closed');

            // Disconnect database
            await disconnectDatabase();

            console.log('✅ Process terminated gracefully');
            process.exit(0);
        });

        // Force exit after 10 seconds
        setTimeout(() => {
            console.error('❌ Forced shutdown after timeout');
            process.exit(1);
        }, 10000);
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

    return server;
}

// Start the server
startServer().catch((error) => {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
});

    server.on('error', (err) => {
        if (err.code === 'EADDRINUSE') {
            console.error(`❌ Port ${PORT} is already in use.`);
            process.exit(1);
        } else {
            console.error('❌ Server error:', err);
            process.exit(1);
        }
    });