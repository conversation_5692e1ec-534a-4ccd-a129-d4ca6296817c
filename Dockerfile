# Base stage for installing dependencies
FROM --platform=linux/amd64 node:20-slim AS base
WORKDIR /app
COPY package*.json ./
COPY prisma ./prisma/
RUN apt-get update && apt-get install -y openssl && \
    npm cache clean --force && \
    npm install --production=false

# Frontend build stage
FROM base AS frontend
COPY . .
# 解决 rollup 依赖问题
RUN rm -rf node_modules package-lock.json && \
    npm install && \
    npm run build

# Backend preparation stage
FROM base AS backend
COPY . .

# Final production image
FROM node:20-slim AS final
WORKDIR /app

# Copy backend code and node_modules
COPY --from=backend /app/backend ./backend
COPY --from=backend /app/node_modules ./node_modules
COPY --from=backend /app/package.json ./
COPY --from=backend /app/prisma ./prisma/

# Copy frontend build output
COPY --from=frontend /app/dist ./dist/

ENV NODE_ENV=production

EXPOSE 3001

CMD ["node", "backend/server.mjs"]
