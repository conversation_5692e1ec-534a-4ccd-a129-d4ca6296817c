#!/bin/bash

# 阿里云ECS自动部署脚本
# Huitong Material 3D材质管理系统

set -e  # 遇到错误立即退出

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
PROJECT_NAME="huitong-material"
PROJECT_DIR="/var/www/${PROJECT_NAME}"
BACKUP_DIR="/var/backups/${PROJECT_NAME}"
NGINX_CONFIG="/etc/nginx/sites-available/${PROJECT_NAME}"
SYSTEMD_SERVICE="/etc/systemd/system/${PROJECT_NAME}.service"
LOG_FILE="/var/log/${PROJECT_NAME}-deploy.log"

# GitHub仓库配置
GITHUB_REPO="https://github.com/Bavoch/huitong-material.git"
BRANCH="aliyun-deploy"

# 数据库配置
DB_NAME="huitong_material"
DB_USER="huitong_user"
DB_HOST="localhost"
DB_PORT="5432"

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        error "此脚本需要root权限运行"
        exit 1
    fi
}

# 检查系统要求
check_system_requirements() {
    log "检查系统要求..."
    
    # 检查操作系统
    if [[ ! -f /etc/os-release ]]; then
        error "无法检测操作系统版本"
        exit 1
    fi
    
    source /etc/os-release
    log "操作系统: $PRETTY_NAME"
    
    # 检查内存
    MEMORY_GB=$(free -g | awk '/^Mem:/{print $2}')
    if [[ $MEMORY_GB -lt 2 ]]; then
        warning "系统内存少于2GB，可能影响性能"
    fi
    
    # 检查磁盘空间
    DISK_SPACE=$(df -BG / | awk 'NR==2 {print $4}' | sed 's/G//')
    if [[ $DISK_SPACE -lt 10 ]]; then
        error "磁盘空间不足10GB，无法继续部署"
        exit 1
    fi
    
    success "系统要求检查通过"
}

# 安装系统依赖
install_system_dependencies() {
    log "安装系统依赖..."
    
    # 更新包管理器
    apt-get update -y
    
    # 安装基础工具
    apt-get install -y \
        curl \
        wget \
        git \
        unzip \
        software-properties-common \
        apt-transport-https \
        ca-certificates \
        gnupg \
        lsb-release
    
    success "系统依赖安装完成"
}

# 安装Node.js
install_nodejs() {
    log "安装Node.js..."
    
    # 检查是否已安装
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        log "Node.js已安装: $NODE_VERSION"
        return
    fi
    
    # 安装Node.js 20.x
    curl -fsSL https://deb.nodesource.com/setup_20.x | bash -
    apt-get install -y nodejs
    
    # 验证安装
    NODE_VERSION=$(node --version)
    NPM_VERSION=$(npm --version)
    
    success "Node.js安装完成: $NODE_VERSION, npm: $NPM_VERSION"
}

# 安装PostgreSQL
install_postgresql() {
    log "安装PostgreSQL..."
    
    # 检查是否已安装
    if command -v psql &> /dev/null; then
        PG_VERSION=$(psql --version)
        log "PostgreSQL已安装: $PG_VERSION"
        return
    fi
    
    # 安装PostgreSQL
    apt-get install -y postgresql postgresql-contrib
    
    # 启动并启用服务
    systemctl start postgresql
    systemctl enable postgresql
    
    success "PostgreSQL安装完成"
}

# 安装Nginx
install_nginx() {
    log "安装Nginx..."
    
    # 检查是否已安装
    if command -v nginx &> /dev/null; then
        NGINX_VERSION=$(nginx -v 2>&1)
        log "Nginx已安装: $NGINX_VERSION"
        return
    fi
    
    # 安装Nginx
    apt-get install -y nginx
    
    # 启动并启用服务
    systemctl start nginx
    systemctl enable nginx
    
    success "Nginx安装完成"
}

# 配置数据库
setup_database() {
    log "配置数据库..."
    
    # 切换到postgres用户并创建数据库和用户
    sudo -u postgres psql << EOF
-- 创建数据库用户
CREATE USER ${DB_USER} WITH PASSWORD '${DB_PASSWORD}';

-- 创建数据库
CREATE DATABASE ${DB_NAME} OWNER ${DB_USER};

-- 授予权限
GRANT ALL PRIVILEGES ON DATABASE ${DB_NAME} TO ${DB_USER};

-- 退出
\q
EOF
    
    success "数据库配置完成"
}

# 创建项目用户
create_project_user() {
    log "创建项目用户..."
    
    # 检查用户是否存在
    if id "www-data" &>/dev/null; then
        log "用户www-data已存在"
    else
        useradd -r -s /bin/false www-data
        success "用户www-data创建完成"
    fi
    
    # 创建项目目录
    mkdir -p $PROJECT_DIR
    mkdir -p $BACKUP_DIR
    mkdir -p /var/log/${PROJECT_NAME}
    
    # 设置目录权限
    chown -R www-data:www-data $PROJECT_DIR
    chown -R www-data:www-data $BACKUP_DIR
    chown -R www-data:www-data /var/log/${PROJECT_NAME}
    
    success "项目用户和目录创建完成"
}

# 克隆项目代码
clone_project() {
    log "克隆项目代码..."
    
    # 备份现有代码（如果存在）
    if [[ -d $PROJECT_DIR/.git ]]; then
        log "备份现有代码..."
        cp -r $PROJECT_DIR ${BACKUP_DIR}/backup-$(date +%Y%m%d-%H%M%S)
    fi
    
    # 克隆或更新代码
    if [[ -d $PROJECT_DIR/.git ]]; then
        cd $PROJECT_DIR
        git fetch origin
        git checkout $BRANCH
        git pull origin $BRANCH
    else
        rm -rf $PROJECT_DIR/*
        git clone -b $BRANCH $GITHUB_REPO $PROJECT_DIR
    fi
    
    # 设置权限
    chown -R www-data:www-data $PROJECT_DIR
    
    success "项目代码更新完成"
}

# 安装项目依赖
install_project_dependencies() {
    log "安装项目依赖..."

    cd $PROJECT_DIR

    # 安装npm依赖
    sudo -u www-data npm ci --production

    # 生成Prisma客户端
    sudo -u www-data npx prisma generate

    # 构建前端
    sudo -u www-data npm run build

    success "项目依赖安装完成"
}

# 配置环境变量
setup_environment() {
    log "配置环境变量..."

    # 创建生产环境配置文件
    cat > $PROJECT_DIR/.env.production << EOF
# 生产环境配置
NODE_ENV=production
PORT=3001

# 数据库配置
POSTGRES_HOST=${DB_HOST}
POSTGRES_PORT=${DB_PORT}
POSTGRES_USER=${DB_USER}
POSTGRES_PASSWORD=${DB_PASSWORD}
POSTGRES_DB=${DB_NAME}

# Prisma数据库连接URL
POSTGRES_PRISMA_URL=postgresql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}?schema=public
POSTGRES_URL_NON_POOLING=postgresql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}?schema=public

# 文件上传配置
UPLOAD_DIR=${PROJECT_DIR}/uploads

# 安全配置
CORS_ORIGIN=https://your-domain.com
EOF

    # 设置文件权限
    chown www-data:www-data $PROJECT_DIR/.env.production
    chmod 600 $PROJECT_DIR/.env.production

    success "环境变量配置完成"
}

# 运行数据库迁移
run_database_migration() {
    log "运行数据库迁移..."

    cd $PROJECT_DIR

    # 设置环境变量
    export NODE_ENV=production
    export POSTGRES_PRISMA_URL="postgresql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}?schema=public"

    # 运行数据库迁移
    sudo -u www-data -E npx prisma db push

    success "数据库迁移完成"
}

# 创建systemd服务
create_systemd_service() {
    log "创建systemd服务..."

    cat > $SYSTEMD_SERVICE << EOF
[Unit]
Description=Huitong Material 3D Management System
After=network.target postgresql.service
Wants=postgresql.service

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory=${PROJECT_DIR}
Environment=NODE_ENV=production
EnvironmentFile=${PROJECT_DIR}/.env.production
ExecStart=/usr/bin/node backend/server.mjs
Restart=always
RestartSec=10
StandardOutput=syslog
StandardError=syslog
SyslogIdentifier=${PROJECT_NAME}

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=${PROJECT_DIR}/uploads /var/log/${PROJECT_NAME}

[Install]
WantedBy=multi-user.target
EOF

    # 重新加载systemd配置
    systemctl daemon-reload
    systemctl enable $PROJECT_NAME

    success "systemd服务创建完成"
}

# 配置Nginx
setup_nginx() {
    log "配置Nginx..."

    cat > $NGINX_CONFIG << EOF
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;

    # 重定向到HTTPS
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;

    # SSL配置
    ssl_certificate /etc/ssl/certs/huitong.crt;
    ssl_certificate_key /etc/ssl/private/huitong.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # 文件上传大小限制
    client_max_body_size 100M;

    # 静态文件
    location /uploads/ {
        alias ${PROJECT_DIR}/uploads/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # API代理
    location /api/ {
        proxy_pass http://127.0.0.1:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }

    # 前端应用
    location / {
        root ${PROJECT_DIR}/dist;
        try_files \$uri \$uri/ /index.html;
        expires 1h;
        add_header Cache-Control "public";
    }
}
EOF

    # 启用站点
    ln -sf $NGINX_CONFIG /etc/nginx/sites-enabled/

    # 测试配置
    nginx -t

    # 重新加载Nginx
    systemctl reload nginx

    success "Nginx配置完成"
}

# 启动服务
start_services() {
    log "启动服务..."

    # 启动应用服务
    systemctl start $PROJECT_NAME
    systemctl status $PROJECT_NAME --no-pager

    success "服务启动完成"
}

# 主函数
main() {
    log "开始阿里云ECS部署..."

    # 检查参数
    if [[ -z "$DB_PASSWORD" ]]; then
        error "请设置数据库密码: export DB_PASSWORD='your_password'"
        exit 1
    fi

    # 执行部署步骤
    check_root
    check_system_requirements
    install_system_dependencies
    install_nodejs
    install_postgresql
    install_nginx
    create_project_user
    setup_database
    clone_project
    install_project_dependencies
    setup_environment
    run_database_migration
    create_systemd_service
    setup_nginx
    start_services

    success "阿里云ECS部署完成！"
    log "应用已启动，请访问 https://your-domain.com 查看"
    log "日志查看: journalctl -u $PROJECT_NAME -f"
}

# 执行主函数
main "$@"
