# 阿里云ECS部署检查清单

## 📋 部署前检查

### 服务器准备
- [ ] ECS实例已创建并运行
- [ ] 服务器规格满足最低要求（2GB内存，20GB存储）
- [ ] 已获取服务器公网IP地址
- [ ] SSH密钥已配置，可以正常连接服务器
- [ ] 服务器时区已设置为正确时区

### 域名和SSL
- [ ] 域名已购买并配置DNS解析
- [ ] DNS解析已生效（可通过ping测试）
- [ ] SSL证书已准备（Let's Encrypt或其他）
- [ ] 防火墙已配置开放80和443端口

### 环境配置
- [ ] 数据库密码已生成（强密码）
- [ ] 环境变量文件已准备
- [ ] 上传目录路径已确定
- [ ] CORS域名已确认

## 🚀 部署过程检查

### 系统依赖安装
- [ ] 系统包管理器已更新
- [ ] Node.js 20.x已安装
- [ ] PostgreSQL已安装并启动
- [ ] Nginx已安装并启动
- [ ] Git已安装
- [ ] 基础工具已安装（curl, wget, unzip等）

### 数据库配置
- [ ] PostgreSQL服务已启动
- [ ] 数据库用户已创建
- [ ] 数据库已创建
- [ ] 用户权限已正确设置
- [ ] 数据库连接测试成功

### 应用部署
- [ ] 项目代码已克隆到正确目录
- [ ] 项目依赖已安装（npm ci）
- [ ] Prisma客户端已生成
- [ ] 前端已构建（npm run build）
- [ ] 数据库迁移已执行
- [ ] 环境变量文件已配置
- [ ] 文件权限已正确设置

### 服务配置
- [ ] systemd服务文件已创建
- [ ] 服务已启用并启动
- [ ] 服务状态检查正常
- [ ] 应用健康检查通过

### Nginx配置
- [ ] Nginx配置文件已创建
- [ ] 配置语法检查通过（nginx -t）
- [ ] SSL证书路径正确
- [ ] 反向代理配置正确
- [ ] 静态文件服务配置正确
- [ ] Nginx已重新加载配置

## ✅ 部署后验证

### 基础功能测试
- [ ] 网站可以通过HTTP访问（重定向到HTTPS）
- [ ] 网站可以通过HTTPS正常访问
- [ ] SSL证书有效且正确
- [ ] 首页加载正常
- [ ] API健康检查接口响应正常（/health）

### 核心功能测试
- [ ] 用户可以正常浏览模型列表
- [ ] 3D模型可以正常加载和显示
- [ ] 材质预览功能正常
- [ ] 管理员登录功能正常
- [ ] 文件上传功能正常
- [ ] 模型管理功能正常
- [ ] 材质管理功能正常

### 性能和安全测试
- [ ] 页面加载速度正常（< 3秒）
- [ ] 文件上传速度正常
- [ ] 大文件上传测试通过
- [ ] 并发访问测试通过
- [ ] 安全头配置正确
- [ ] HTTPS强制重定向正常
- [ ] 文件访问权限正确

## 🔧 系统配置检查

### 监控和日志
- [ ] 应用日志正常输出
- [ ] Nginx访问日志正常记录
- [ ] 系统日志监控配置
- [ ] 错误日志监控配置
- [ ] 日志轮转配置正确

### 备份配置
- [ ] 数据库备份脚本已配置
- [ ] 文件备份脚本已配置
- [ ] 定时备份任务已设置
- [ ] 备份恢复测试通过
- [ ] 备份存储空间充足

### 安全配置
- [ ] 防火墙规则已配置
- [ ] SSH安全配置已优化
- [ ] fail2ban已安装并配置
- [ ] 系统更新已应用
- [ ] 不必要的服务已禁用

## 📊 性能优化检查

### 应用优化
- [ ] Node.js内存限制已设置
- [ ] 数据库连接池已配置
- [ ] 静态文件缓存已配置
- [ ] Gzip压缩已启用
- [ ] CDN配置（如需要）

### 数据库优化
- [ ] PostgreSQL配置已优化
- [ ] 数据库索引已创建
- [ ] 查询性能已测试
- [ ] 连接数限制已设置

### 服务器优化
- [ ] 系统资源监控已配置
- [ ] 内存使用优化
- [ ] 磁盘I/O优化
- [ ] 网络配置优化

## 🚨 故障恢复准备

### 备份验证
- [ ] 数据库备份文件完整性验证
- [ ] 文件备份完整性验证
- [ ] 备份恢复流程测试
- [ ] 灾难恢复计划已制定

### 监控告警
- [ ] 服务状态监控已配置
- [ ] 资源使用监控已配置
- [ ] 错误日志监控已配置
- [ ] 告警通知已配置

### 文档和联系方式
- [ ] 部署文档已更新
- [ ] 运维手册已准备
- [ ] 紧急联系方式已确认
- [ ] 技术支持渠道已建立

## 📝 部署记录

### 部署信息
- 部署日期：__________
- 部署人员：__________
- 服务器IP：__________
- 域名：__________
- 数据库版本：__________
- Node.js版本：__________
- 应用版本：__________

### 配置信息
- 数据库名：__________
- 数据库用户：__________
- 上传目录：__________
- SSL证书类型：__________
- 备份策略：__________

### 测试结果
- 功能测试：□ 通过 □ 失败
- 性能测试：□ 通过 □ 失败
- 安全测试：□ 通过 □ 失败
- 备份测试：□ 通过 □ 失败

### 问题记录
问题描述：
解决方案：
负责人：
解决时间：

### 签字确认
部署工程师：__________ 日期：__________
测试工程师：__________ 日期：__________
项目负责人：__________ 日期：__________

---

## 📞 紧急联系方式

- 技术支持：[联系方式]
- 服务器提供商：[阿里云客服]
- 域名服务商：[联系方式]
- SSL证书提供商：[联系方式]

## 📚 相关文档

- [部署指南](./README.md)
- [故障排除指南](./TROUBLESHOOTING.md)
- [运维手册](./OPERATIONS.md)
- [安全配置指南](./SECURITY.md)
