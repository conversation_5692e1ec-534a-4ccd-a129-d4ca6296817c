# 阿里云ECS部署指南

Huitong Material 3D材质管理系统阿里云ECS部署文档

## 📋 部署前准备

### 系统要求

- **操作系统**: Ubuntu 20.04 LTS 或更高版本
- **内存**: 最少2GB，推荐4GB或更多
- **存储**: 最少20GB可用空间
- **网络**: 公网IP地址，开放80和443端口

### 域名配置

1. 购买域名并配置DNS解析到ECS公网IP
2. 申请SSL证书（推荐使用Let's Encrypt免费证书）

## 🚀 快速部署

### 方式一：自动化脚本部署（推荐）

1. **连接到ECS服务器**
```bash
ssh root@your-server-ip
```

2. **下载部署脚本**
```bash
wget https://raw.githubusercontent.com/Bavoch/huitong-material/aliyun-deploy/deploy/aliyun-deploy.sh
chmod +x aliyun-deploy.sh
```

3. **设置数据库密码**
```bash
export DB_PASSWORD='your_secure_password_here'
```

4. **运行部署脚本**
```bash
./aliyun-deploy.sh
```

### 方式二：Docker Compose部署

1. **安装Docker和Docker Compose**
```bash
# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# 安装Docker Compose
curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose
```

2. **克隆项目代码**
```bash
git clone -b aliyun-deploy https://github.com/Bavoch/huitong-material.git
cd huitong-material
```

3. **配置环境变量**
```bash
cp .env.production.example .env.production
# 编辑 .env.production 文件，设置数据库密码等配置
```

4. **启动服务**
```bash
docker-compose -f deploy/docker-compose.aliyun.yml up -d
```

## ⚙️ 配置说明

### 环境变量配置

创建 `.env.production` 文件：

```bash
# 数据库配置
POSTGRES_USER=huitong_user
POSTGRES_PASSWORD=your_secure_password
POSTGRES_DB=huitong_material
POSTGRES_HOST=localhost
POSTGRES_PORT=5432

# 应用配置
NODE_ENV=production
PORT=3001

# 文件上传配置
UPLOAD_DIR=/var/www/huitong-material/uploads

# 安全配置
CORS_ORIGIN=https://your-domain.com
```

### SSL证书配置

#### 使用Let's Encrypt免费证书

1. **安装Certbot**
```bash
apt-get update
apt-get install certbot python3-certbot-nginx
```

2. **申请证书**
```bash
certbot --nginx -d your-domain.com -d www.your-domain.com
```

3. **设置自动续期**
```bash
crontab -e
# 添加以下行
0 12 * * * /usr/bin/certbot renew --quiet
```

#### 使用自签名证书（仅用于测试）

```bash
# 创建SSL目录
mkdir -p /etc/ssl/private /etc/ssl/certs

# 生成私钥
openssl genrsa -out /etc/ssl/private/huitong.key 2048

# 生成证书
openssl req -new -x509 -key /etc/ssl/private/huitong.key -out /etc/ssl/certs/huitong.crt -days 365
```

### 防火墙配置

```bash
# 安装ufw
apt-get install ufw

# 配置防火墙规则
ufw default deny incoming
ufw default allow outgoing
ufw allow ssh
ufw allow 80/tcp
ufw allow 443/tcp

# 启用防火墙
ufw enable
```

## 🔧 服务管理

### 查看服务状态

```bash
# 查看应用服务状态
systemctl status huitong-material

# 查看应用日志
journalctl -u huitong-material -f

# 查看Nginx状态
systemctl status nginx

# 查看数据库状态
systemctl status postgresql
```

### 重启服务

```bash
# 重启应用
systemctl restart huitong-material

# 重启Nginx
systemctl restart nginx

# 重启数据库
systemctl restart postgresql
```

### Docker Compose管理

```bash
# 查看容器状态
docker-compose -f deploy/docker-compose.aliyun.yml ps

# 查看日志
docker-compose -f deploy/docker-compose.aliyun.yml logs -f

# 重启服务
docker-compose -f deploy/docker-compose.aliyun.yml restart

# 停止服务
docker-compose -f deploy/docker-compose.aliyun.yml down

# 更新服务
docker-compose -f deploy/docker-compose.aliyun.yml pull
docker-compose -f deploy/docker-compose.aliyun.yml up -d
```

## 📊 监控和维护

### 性能监控

1. **安装监控工具**
```bash
apt-get install htop iotop nethogs
```

2. **查看系统资源使用情况**
```bash
# CPU和内存使用情况
htop

# 磁盘I/O
iotop

# 网络使用情况
nethogs
```

### 日志管理

```bash
# 应用日志
tail -f /var/log/huitong-material.log

# Nginx访问日志
tail -f /var/log/nginx/access.log

# Nginx错误日志
tail -f /var/log/nginx/error.log

# 系统日志
tail -f /var/log/syslog
```

### 数据备份

1. **数据库备份**
```bash
# 创建备份脚本
cat > /usr/local/bin/backup-db.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/var/backups/huitong-material"
DATE=$(date +%Y%m%d_%H%M%S)
mkdir -p $BACKUP_DIR
pg_dump -U huitong_user -h localhost huitong_material > $BACKUP_DIR/db_backup_$DATE.sql
find $BACKUP_DIR -name "db_backup_*.sql" -mtime +30 -delete
EOF

chmod +x /usr/local/bin/backup-db.sh

# 设置定时备份
crontab -e
# 添加以下行（每天凌晨2点备份）
0 2 * * * /usr/local/bin/backup-db.sh
```

2. **文件备份**
```bash
# 创建文件备份脚本
cat > /usr/local/bin/backup-files.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/var/backups/huitong-material"
DATE=$(date +%Y%m%d_%H%M%S)
mkdir -p $BACKUP_DIR
tar -czf $BACKUP_DIR/uploads_backup_$DATE.tar.gz -C /var/www/huitong-material uploads
find $BACKUP_DIR -name "uploads_backup_*.tar.gz" -mtime +30 -delete
EOF

chmod +x /usr/local/bin/backup-files.sh

# 设置定时备份
crontab -e
# 添加以下行（每天凌晨3点备份）
0 3 * * * /usr/local/bin/backup-files.sh
```

## 🔒 安全配置

### 系统安全

1. **更新系统**
```bash
apt-get update && apt-get upgrade -y
```

2. **配置SSH安全**
```bash
# 编辑SSH配置
nano /etc/ssh/sshd_config

# 修改以下配置
Port 22
PermitRootLogin no
PasswordAuthentication no
PubkeyAuthentication yes

# 重启SSH服务
systemctl restart ssh
```

3. **安装fail2ban**
```bash
apt-get install fail2ban

# 配置fail2ban
cat > /etc/fail2ban/jail.local << 'EOF'
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 5

[sshd]
enabled = true

[nginx-http-auth]
enabled = true

[nginx-limit-req]
enabled = true
EOF

systemctl enable fail2ban
systemctl start fail2ban
```

## 🚨 故障排除

### 常见问题

1. **应用无法启动**
```bash
# 检查环境变量
cat /var/www/huitong-material/.env.production

# 检查数据库连接
sudo -u postgres psql -c "\l"

# 检查端口占用
netstat -tlnp | grep 3001
```

2. **文件上传失败**
```bash
# 检查上传目录权限
ls -la /var/www/huitong-material/uploads

# 检查磁盘空间
df -h

# 检查Nginx配置
nginx -t
```

3. **SSL证书问题**
```bash
# 检查证书有效期
openssl x509 -in /etc/ssl/certs/huitong.crt -text -noout

# 测试SSL配置
openssl s_client -connect your-domain.com:443
```

### 联系支持

如果遇到问题，请：

1. 查看相关日志文件
2. 检查系统资源使用情况
3. 确认配置文件正确性
4. 提交Issue到GitHub仓库

## 📚 相关文档

- [项目README](../README.md)
- [开发指南](../docs/DEVELOPMENT.md)
- [API文档](../docs/API.md)
- [阿里云ECS文档](https://help.aliyun.com/product/25365.html)
