version: '3.8'

# 阿里云ECS Docker Compose 配置
# Huitong Material 3D材质管理系统

services:
  # PostgreSQL 数据库
  database:
    image: postgres:15-alpine
    container_name: huitong_db
    restart: unless-stopped
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-huitong_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB:-huitong_material}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --locale=C"
    ports:
      - "127.0.0.1:5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d:ro
    networks:
      - huitong_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-huitong_user} -d ${POSTGRES_DB:-huitong_material}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M

  # 应用服务
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: final
    container_name: huitong_app
    restart: unless-stopped
    depends_on:
      database:
        condition: service_healthy
    environment:
      NODE_ENV: production
      PORT: 3001
      POSTGRES_HOST: database
      POSTGRES_PORT: 5432
      POSTGRES_USER: ${POSTGRES_USER:-huitong_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB:-huitong_material}
      POSTGRES_PRISMA_URL: postgresql://${POSTGRES_USER:-huitong_user}:${POSTGRES_PASSWORD}@database:5432/${POSTGRES_DB:-huitong_material}?schema=public
      POSTGRES_URL_NON_POOLING: postgresql://${POSTGRES_USER:-huitong_user}:${POSTGRES_PASSWORD}@database:5432/${POSTGRES_DB:-huitong_material}?schema=public
      UPLOAD_DIR: /app/uploads
    ports:
      - "127.0.0.1:3001:3001"
    volumes:
      - uploads_data:/app/uploads
      - app_logs:/app/logs
    networks:
      - huitong_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "3"

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: huitong_nginx
    restart: unless-stopped
    depends_on:
      app:
        condition: service_healthy
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./ssl:/etc/nginx/ssl:ro
      - uploads_data:/var/www/uploads:ro
      - nginx_logs:/var/log/nginx
    networks:
      - huitong_network
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.1'
          memory: 64M

  # Redis 缓存 (可选)
  redis:
    image: redis:7-alpine
    container_name: huitong_redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-huitong_redis_pass}
    ports:
      - "127.0.0.1:6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - huitong_network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.1'
          memory: 64M

# 网络配置
networks:
  huitong_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷配置
volumes:
  postgres_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /var/lib/docker/volumes/huitong_postgres_data/_data
  
  uploads_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /var/www/huitong-material/uploads
  
  redis_data:
    driver: local
  
  app_logs:
    driver: local
  
  nginx_logs:
    driver: local

# 配置文件
configs:
  nginx_config:
    file: ./nginx/nginx.conf
  
  app_env:
    file: ./.env.production

# 密钥管理
secrets:
  postgres_password:
    external: true
  
  redis_password:
    external: true
  
  ssl_cert:
    file: ./ssl/huitong.crt
  
  ssl_key:
    file: ./ssl/huitong.key
