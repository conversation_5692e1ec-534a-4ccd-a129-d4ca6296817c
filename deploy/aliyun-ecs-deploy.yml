# 阿里云ECS部署配置文件
# Huitong Material 3D材质管理系统

version: '3.8'

# 环境配置
environment:
  # 应用配置
  NODE_ENV: production
  PORT: 3001
  
  # 数据库配置 (需要根据实际情况修改)
  POSTGRES_HOST: localhost
  POSTGRES_PORT: 5432
  POSTGRES_USER: huitong_user
  POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
  POSTGRES_DB: huitong_material
  
  # Prisma数据库连接URL
  POSTGRES_PRISMA_URL: postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB}?schema=public
  POSTGRES_URL_NON_POOLING: postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB}?schema=public
  
  # 文件上传配置
  UPLOAD_DIR: /var/www/huitong-material/uploads
  
  # 安全配置
  CORS_ORIGIN: https://your-domain.com
  
# 服务配置
services:
  # 应用服务
  app:
    build_context: .
    working_directory: /var/www/huitong-material
    user: www-data
    group: www-data
    
    # 端口映射
    ports:
      - "3001:3001"
    
    # 数据卷挂载
    volumes:
      - ./uploads:/var/www/huitong-material/uploads
      - ./logs:/var/www/huitong-material/logs
    
    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
  
  # PostgreSQL数据库服务
  database:
    image: postgres:15-alpine
    restart: always
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --locale=C"
    
    ports:
      - "5432:5432"
    
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    
    # 数据库健康检查
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5

# 网络配置
networks:
  huitong_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷配置
volumes:
  postgres_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /var/lib/postgresql/data

# 部署配置
deploy:
  # 资源限制
  resources:
    limits:
      cpus: '2.0'
      memory: 4G
    reservations:
      cpus: '1.0'
      memory: 2G
  
  # 重启策略
  restart_policy:
    condition: on-failure
    delay: 5s
    max_attempts: 3
    window: 120s
  
  # 更新配置
  update_config:
    parallelism: 1
    delay: 10s
    failure_action: rollback
    monitor: 60s
    max_failure_ratio: 0.3

# 监控配置
monitoring:
  # 日志配置
  logging:
    driver: "json-file"
    options:
      max-size: "100m"
      max-file: "3"
  
  # 指标收集
  metrics:
    enabled: true
    port: 9090
    path: /metrics

# 安全配置
security:
  # 防火墙规则
  firewall:
    - port: 22
      protocol: tcp
      source: admin_ip_range
    - port: 80
      protocol: tcp
      source: 0.0.0.0/0
    - port: 443
      protocol: tcp
      source: 0.0.0.0/0
    - port: 3001
      protocol: tcp
      source: 127.0.0.1/32
  
  # SSL/TLS配置
  ssl:
    enabled: true
    cert_path: /etc/ssl/certs/huitong.crt
    key_path: /etc/ssl/private/huitong.key
    
# 备份配置
backup:
  # 数据库备份
  database:
    enabled: true
    schedule: "0 2 * * *"  # 每天凌晨2点
    retention_days: 30
    backup_path: /var/backups/huitong-material/database
  
  # 文件备份
  uploads:
    enabled: true
    schedule: "0 3 * * *"  # 每天凌晨3点
    retention_days: 30
    backup_path: /var/backups/huitong-material/uploads

# 性能优化
performance:
  # Node.js优化
  nodejs:
    max_old_space_size: 2048
    max_semi_space_size: 128
  
  # 数据库优化
  postgresql:
    shared_buffers: 256MB
    effective_cache_size: 1GB
    work_mem: 4MB
    maintenance_work_mem: 64MB
    max_connections: 100

# 开发工具配置
development:
  # 调试配置
  debug:
    enabled: false
    port: 9229
  
  # 热重载
  hot_reload:
    enabled: false
